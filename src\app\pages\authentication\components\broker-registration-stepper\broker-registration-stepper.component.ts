import {
  Component,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  OnInit,
} from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { BrokerRegistrationData } from '../../models';
import { ProjectsService } from '../../../developer/services/projects.service';
import { AuthenticationService } from '../../services/authentication.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../../modules/i18n/translation.service';



const SPECIALIZATION_MAPPING: Record<string, string[]> = {
  purchase_sell_outside_compound: [
    'purchasing_sell_residential_outside_compound',
    'purchasing_sell_national_housing_projects_outside_compound',
    'purchasing_sell_administrative_commercial_units_outside_compound',
    'purchasing_sell_industrial_and_warehousing_outside_compound',
    'purchasing_sell_lands_and_ready_projects_outside_compound',
    'purchasing_sell_villas_and_buildings_outside_compound',
  ],
  // purchase_sell_inside_compound: [
  //   'purchasing_sell_residential_inside_compound',
  //   'purchasing_sell_villas_inside_compound',
  //   'purchasing_sell_administrative_commercial_Units_inside_compound',
  // ],
  primary_inside_compound: [
    'purchasing_sell_residential_inside_compound',
    'purchasing_sell_villas_inside_compound',
    'purchasing_sell_administrative_commercial_units_inside_compound',
  ],
  resale_inside_compound: [
    'purchasing_sell_residential_inside_compound',
    'purchasing_sell_villas_inside_compound',
    'purchasing_sell_administrative_commercial_units_inside_compound',
  ],
  rentals_outside_compound: [
    'rent_residential_outside_compound',
    'rent_national_housing_projects_outside_compound',
    'rent_administrative_commercial_units_outside_compound',
    'rent_industrial_and_warehousing_outside_compound',
    'rent_hotel_Units_outside_compound'
  ],
  rentals_inside_compound: [
    'rent_residential_inside_compound',
    'rent_hotel_Units_inside_compound',
    'rent_administrative_commercial_units_inside_compound',
  ],
};


interface SpecializationScope {
  specialization_scope: string;
  specializations: string[];
  expanded: boolean;
  selected: boolean;
  selectedTypes?: string[];
}

@Component({
  selector: 'app-broker-registration-stepper',
  templateUrl: './broker-registration-stepper.component.html',
  styleUrls: ['./broker-registration-stepper.component.scss'],
})
export class BrokerRegistrationStepperComponent implements OnInit {
  @Output() onBack = new EventEmitter<void>();
  @Output() onComplete = new EventEmitter<BrokerRegistrationData>();

  registrationForm: FormGroup;
  currentStep = 1;
  readonly totalSteps = 8;
  uploadedFiles: { [key: string]: File[] } = {};
  brokerType = '';
  verificationDigits: string[] = ['', '', '', '', ''];
  countdown: number = 25;
  showResendButton: boolean = false;
  isLoadingSendOtp: boolean = false;
  isLoadingCheckOtp: boolean = false;
  isLoadingCreateAccount: boolean = false;
  otpErrorMessage: string = '';
  createAccountErrorMessage: string = '';

  // Validators
  static noNumbers = Validators.pattern(/^[^0-9]*$/);
  static emailOrPhonePattern = Validators.pattern(/^([^\s@]+@[^\s@]+\.[^\s@]+|01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);
  static phonePattern = Validators.pattern(/^(01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);
  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/);

  // Location data
  cities: any[] = [];
  areas: any[] = [];
  subAreas: any[] = [];
  selectedCityId: number = 0;
  selectedCityName = '';
  selectedAreaId: number = 0;
  selectedAreaName = '';
  selectedSubAreaId: number = 0;
  selectedSubAreaName = '';
  step2Form: FormGroup;

  // Specializations
  staticScopes: SpecializationScope[] = [];

  constructor(
    private fb: FormBuilder,
    private projectsService: ProjectsService,
    private cd: ChangeDetectorRef,
    private authenticationService: AuthenticationService,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    this.registrationForm = this.createForm();

    this.step2Form = this.fb.group({
      cityId: [0],
      areaId: [0],
      subAreaId:[0],
    });

    this.initializeSpecializations();
    this.loadCities();
  }

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();

    // Set the language in TranslateService
    this.translateService.use(currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Step 1: Basic Information
      fullName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          BrokerRegistrationStepperComponent.noNumbers,
        ],
      ],
      input: [
        '',
        [Validators.required, BrokerRegistrationStepperComponent.emailOrPhonePattern],
      ],

      // Step 2: Verification Code
      verificationCode: this.fb.array(
        Array(5)
          .fill('')
          .map(() =>
            this.fb.control('', [
              Validators.required,
              Validators.pattern('[0-9]'),
            ])
          )
      ),

      // Step 7: Phone, Email, Password, Terms
      phone: ['', [Validators.required, BrokerRegistrationStepperComponent.phonePattern]],
      email: ['', [Validators.email]],
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          BrokerRegistrationStepperComponent.passwordPattern,
        ],
      ],
      password_confirmation: ['', [Validators.required]],
      agreeTerms: [false, [Validators.requiredTrue]],
    });
  }

  private initializeSpecializations(): void {
      this.staticScopes = Object.entries(SPECIALIZATION_MAPPING).map(([scope, specializations]) => ({
      specialization_scope: scope,
      specializations,
      expanded: false,
      selected: false,
      selectedTypes: [],
    }));
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      // Clear OTP error message when leaving step 2
      if (this.currentStep === 2) {
        this.otpErrorMessage = '';
      }
      this.currentStep++;
    }
  }

  nextStepSkipping(): void {
    if (this.currentStep < this.totalSteps) {
      // Clear OTP error message when leaving step 2
      if (this.currentStep === 2) {
        this.otpErrorMessage = '';
      }

      if(this.currentStep == 5){
        this.currentStep = 6;
      }
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.onBack.emit();
    }
  }

  onFileChange(event: Event, fileType: string): void {
    const files = (event.target as HTMLInputElement).files;
    if (!files?.length) return;

    // Clear previous files and add new ones (replace instead of append)
    this.uploadedFiles[fileType] = Array.from(files);
    console.log(`Uploaded ${files.length} file(s) for ${fileType} (replaced previous files)`);
  }

  getFileCount(fileType: string): number {
    return this.uploadedFiles[fileType]?.length || 0;
  }

  // Clear files for a specific file type
  clearFiles(fileType: string): void {
    delete this.uploadedFiles[fileType];
    console.log(`Cleared all files for ${fileType}`);
  }

  // Clear all uploaded files
  clearAllFiles(): void {
    this.uploadedFiles = {};
    console.log('Cleared all uploaded files');
  }

  // Location Methods
  loadCities(): void {
    this.projectsService.getCities().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.projectsService.getAreas(cityId).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }

  loadSubAreas(areaId?: number): void {
    this.projectsService.getSubAreas(areaId).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.subAreas = response.data;
        } else {
          console.warn('No areas data in response');
          this.subAreas = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading areas:', err);
        this.subAreas = [];
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }

  onCityChange(event: any): void {
    const cityId = event.target.value;
    this.selectedCityId = cityId;
    this.selectedCityName =
      event.target.options[event.target.selectedIndex].text;
    this.step2Form.patchValue({ cityId: cityId });

    // Reset area
    this.selectedAreaId = 0;
    this.selectedAreaName = '';
    this.step2Form.patchValue({ areaId: 0 });
    this.areas = [];

    this.loadAreas(cityId);
  }

  onAreaChange(event: any): void {
    const areaId = event.target.value;
    this.selectedAreaId = areaId;
    this.selectedAreaName =
      event.target.options[event.target.selectedIndex].text;
    this.step2Form.patchValue({ areaId: areaId });

    this.loadSubAreas(areaId);
  }

   onSubAreaChange(event: any): void {
    const subAreaId = event.target.value;
    this.selectedSubAreaId = subAreaId;
    this.selectedSubAreaName = event.target.options[event.target.selectedIndex].text;
    this.step2Form.patchValue({ subAreaId: subAreaId });
  }

  // Specialization Methods
  toggleSpecializationScope(scope: SpecializationScope): void {
    this.staticScopes.forEach((s) => {
      if (s !== scope) s.expanded = false;
    });
    scope.expanded = !scope.expanded;
  }

  onScopeSelectionChange(scope: SpecializationScope, event: any): void {
    scope.selected = event.target.checked;
    console.log(scope);
  }

  onSpecializationChange(scope: SpecializationScope, type: string, event: any): void {
    if (!scope.selectedTypes) {
      scope.selectedTypes = [];
    }

    if (event.target.checked) {
      if (!scope.selectedTypes.includes(type)) {
        scope.selectedTypes.push(type);
      }
    } else {
      scope.selectedTypes = scope.selectedTypes.filter(t => t !== type);
    }

    console.log(this.getSpecializationScopesPayload());
  }

  // Utility Methods
  isOtherScopeExpanded(currentScope: SpecializationScope): boolean {
    return this.staticScopes.some(
      (scope) => scope !== currentScope && scope.expanded
    );
  }

  // Simple action methods
  selectBrokerType(type: string): void {
    this.brokerType = type;
    console.log(this.brokerType);
  }

  get verificationCodeControls() {
    return (this.registrationForm.get('verificationCode') as FormArray)
      .controls;
  }

  onDigitInput(index: number): void {
    const code = this.verificationDigits.join('');
    this.registrationForm.patchValue({ verificationCode: code });
  }

  // Helper methods for validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.registrationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  markFieldAsTouched(fieldName: string): void {
    this.registrationForm.get(fieldName)?.markAsTouched();
  }

  getFieldError(fieldName: string): string {
    const field = this.registrationForm.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;
    if (errors['required']) return 'This field is required';
    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';
    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';
    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';
    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';
    if (errors['email']) return 'Enter valid email';
    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;

    return 'Invalid input';
  }

  getFormError(): string {
    const password = this.registrationForm.get('password')?.value;
    const confirmPassword = this.registrationForm.get('password_confirmation')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      return 'Passwords do not match';
    }
    return '';
  }

  // Check if step is valid
  isStep1Valid(): boolean {
    const fullName = this.registrationForm.get('fullName');
    const input = this.registrationForm.get('input');

    return !!(fullName?.valid && input?.valid);
  }

  isStep2Valid(): boolean {
    const verificationCode = this.registrationForm.get('verificationCode') as FormArray;
    return verificationCode.valid;
  }

  isStep7Valid(): boolean {
    const phone = this.registrationForm.get('phone');
    const email = this.registrationForm.get('email');
    const password = this.registrationForm.get('password');
    const passwordConfirmation = this.registrationForm.get('password_confirmation');
    const agreeTerms = this.registrationForm.get('agreeTerms');

    // Check if passwords match
    const passwordsMatch = password?.value === passwordConfirmation?.value;

    return !!(
      phone?.valid &&
      email?.valid &&
      password?.valid &&
      passwordConfirmation?.valid &&
      agreeTerms?.valid &&
      passwordsMatch
    );
  }

  handleNextStepAndSendCode(): void {
    this.sendVerificationCode(true);
  }

  sendVerificationCode(moveToNextStep: boolean = false) {
    if (!this.isStep1Valid()) {
      this.markFieldAsTouched('fullName');
      this.markFieldAsTouched('input');
      return;
    }

    this.isLoadingSendOtp = true;
    this.otpErrorMessage = '';
    const input = this.registrationForm.get('input')?.value?.trim();

    let params: { email?: string; phone?: string } = {};
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }

    this.authenticationService.sendOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP sent:', response);
        this.isLoadingSendOtp = false;
        this.startCountdown();
        if (moveToNextStep) {
          this.nextStep();
        }
        this.cd.markForCheck();
      },
      error: (error: any) => {
        console.error('Failed to send OTP:', error);
        this.isLoadingSendOtp = false;
        this.otpErrorMessage = error.message || 'Failed to send verification code. Please try again.';
        this.cd.markForCheck();
      }
    });
  }

  checkOTP() {
    if (!this.isStep2Valid()) {
      // Mark all verification code inputs as touched
      const verificationCodeArray = this.registrationForm.get('verificationCode') as FormArray;
      verificationCodeArray.controls.forEach(control => control.markAsTouched());
      return;
    }

    this.isLoadingCheckOtp = true;
    this.otpErrorMessage = '';
    const input = this.registrationForm.get('input')?.value?.trim();
    const codeArray = this.registrationForm.get('verificationCode')?.value;
    const otp = codeArray.join('');

    let params: { email?: string; phone?: string; otp?: number } = {};
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }

    params.otp = otp;

    this.authenticationService.checkOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP checked:', response);
        this.isLoadingCheckOtp = false;
        this.nextStep();
        this.cd.markForCheck();
      },
      error: (error: any) => {
        console.error('Failed to check OTP:', error);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';
        this.cd.markForCheck();
      }
    });
  }

  createAccount(): void {
    if (!this.isStep7Valid()) {
      this.markFieldAsTouched('phone');
      this.markFieldAsTouched('email');
      this.markFieldAsTouched('password');
      this.markFieldAsTouched('password_confirmation');
      this.markFieldAsTouched('agreeTerms');
      return;
    }

    this.isLoadingCreateAccount = true;
    this.createAccountErrorMessage = '';
    const input = this.registrationForm.get('input')?.value?.trim();
    const emailValue = this.registrationForm.get('email')?.value?.trim();

    let params = this.registrationForm.value;
    params.role = 'broker';
    params.type = this.brokerType;
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }

    // Handle optional email field - only include if it has a value
    if (emailValue && emailValue.length > 0) {
      params.email = emailValue;
    } else {
      // Remove email from params if it's empty
      delete params.email;
    }

    const specializationParams = this.getSpecializationScopesPayload();
    const formData = new FormData();

    // Append all form fields
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        formData.append(key, params[key]);
      }
    }

    if(this.selectedAreaId != 0){
      params.areaIds = [this.selectedAreaId];
      params.areaIds?.forEach((id: any) => formData.append('areaIds[]', id));
    }

    if(this.selectedSubAreaId != 0){
      params.subAreas = [this.selectedSubAreaId];
      params.subAreas?.forEach((id: any) => formData.append('subAreas[]', id));
    }

    // Append specializationScopes (array-like)
    for (const key in specializationParams) {
      const values = specializationParams[key];
      values.forEach(value => {
        formData.append(key, value);
      });
    }

    // Append uploaded files (if any)
    for (const fileType in this.uploadedFiles) {
      const files = this.uploadedFiles[fileType];
      if (files?.length) {
        // Append all files for each field type, replacing any previous files
        files.forEach((file) => {
          formData.append(fileType, file);
        });
      }
    }

    this.authenticationService.register(formData).subscribe({
      next: (response: any) => {
        let user = response.data;
        localStorage.setItem('authToken', user.authToken);
        this.authenticationService.setCurrentUser(response.data);
        this.isLoadingCreateAccount = false;
        this.nextStep();
        this.cd.markForCheck();
      },
      error: (error: any) => {
        this.isLoadingCreateAccount = false;
        this.createAccountErrorMessage = error?.error?.message || 'Failed to create account. Please try again.';
        this.cd.markForCheck();
      }
    });
  }

  startCountdown() {
    this.showResendButton = false;
    this.countdown = 25;

    const intervalId = setInterval(() => {
      this.countdown--;
      if (this.countdown === 0) {
        clearInterval(intervalId);
        this.showResendButton = true;
      }
      this.cd.markForCheck();
    }, 1000);
  }

  autoFocusNext(event: any, index: number): void {
    const input = event.target;
    if (input.value && index < 5) {
      const nextInput =
        input.parentElement?.nextElementSibling?.querySelector('input');
      nextInput?.focus();
    }
  }

  private clearOtpInputs() {
    this.verificationDigits = ['', '', '', '', ''];
    const verificationCodeArray = this.registrationForm.get(
      'verificationCode'
    ) as FormArray;
    verificationCodeArray.controls.forEach((control) => {
      control.setValue('');
      control.markAsUntouched();
      control.markAsPristine();
    });
  }

  onResendCode() {
    this.clearOtpInputs();
    this.otpErrorMessage = '';
    this.sendVerificationCode(false);
  }

  toSnakeCase(str: string): string {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_')
      .replace(/^_|_$/g, '');
  }

  getSpecializationScopesPayload(): { [key: string]: string[] } {
    const result: { [key: string]: string[] } = {};

    for (const scope of this.staticScopes) {
      if (scope.selectedTypes && scope.selectedTypes.length > 0) {
        const key = this.toSnakeCase(scope.specialization_scope);
        result[`specializationScopes[${key}][]`] = scope.selectedTypes.map(type =>
          this.toSnakeCase(type)
        );
      }
    }

    return result;
  }

  specializationDisplayMap: { [key: string]: string } = {
    'purchasing_sell_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - studio',
    'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
    'purchasing_sell_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'purchasing_sell_industrial_and_warehousing_outside_compound': 'Factories - Warehouses - Industrial Lands - Warehouse Lands',
    'purchasing_sell_lands_and_ready_projects_outside_compound': 'Administrative & Commercial Lands - Commercial Administrative Malls',
    'purchasing_sell_villas_and_buildings_outside_compound': 'Villas - Full Buildings - Residential Lands - Concrete Structure',
    'purchasing_sell_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - studio',
    'purchasing_sell_villas_inside_compound': 'Villas - Standalone - town house - twin house',
    'purchasing_sell_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'rent_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - villas - studio',
    'rent_hotel_Units_inside_compound': 'Hotel Units',
    'rent_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'rent_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - villas - studio',
    'rent_national_housing_projects_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
    'rent_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',
    'rent_industrial_and_warehousing_outside_compound': 'Factories - Warehouses',
    'rent_hotel_units_outside_compound': 'Hotel Units'
  };


}
