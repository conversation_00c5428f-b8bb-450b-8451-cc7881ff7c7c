import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-registration-papers',
  templateUrl: './registration-papers.component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, NgbModalModule, TranslateModule],
})
export class RegistrationPapersComponent {
  documents: any[] = [];

  constructor(private modalService: NgbModal, private translate: TranslateService) {
    // Load sample documents
    this.loadSampleDocuments();
  }

  // Load sample documents for demonstration
  loadSampleDocuments() {
    this.documents = [
      {
        id: 1,
        type: 'JPG',
        fileUrl: './assets/media/avatars/300-1.jpg',
        description: 'ID Card',
        status: 'approved',
      },
      {
        id: 2,
        type: 'PDF',
        fileUrl: './assets/media/svg/files/pdf.svg',
        description: 'Property Contract',
        status: 'pending',
      },
      {
        id: 3,
        type: 'JPG',
        fileUrl: './assets/media/avatars/300-2.jpg',
        description: 'Property Photo',
        status: 'approved',
      },
    ];
  }

  formData = {
    mode: 'add',
    file: null as File | null,
    preview: '',
    description: '',
    documentId: null as number | null,
  };

  isLoading = false;

  // Handle File Selection
  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.formData.file = file;

      const reader = new FileReader();
      reader.onload = () => {
        this.formData.preview = reader.result as string;
      };
      reader.readAsDataURL(file);
    }
  }

  // Open Add/Edit Modal
  openModal(modal: any, mode: 'add' | 'edit', document?: any) {
    this.formData.mode = mode;

    if (mode === 'edit' && document) {
      this.formData.documentId = document.id;
      this.formData.preview = document.fileUrl;
      this.formData.file = null;
    } else {
      this.formData = {
        mode: 'add',
        file: null,
        preview: '',
        description: '',
        documentId: null,
      };
    }

    this.modalService.open(modal, { centered: true });
  }

  // Fake Save Document
  saveDocument() {
    this.isLoading = true;

    const newDoc = {
      id: this.formData.documentId ?? Date.now(),
      type: this.formData.file
        ? this.getFileType(this.formData.file)
        : 'UNKNOWN',
      fileUrl: this.formData.preview,
      description: this.formData.file ? this.formData.file.name : 'Document',
      status: 'pending',
    };

    setTimeout(() => {
      if (this.formData.mode === 'edit') {
        const index = this.documents.findIndex(
          (doc) => doc.id === this.formData.documentId
        );
        if (index !== -1) this.documents[index] = newDoc;
      } else {
        this.documents.push(newDoc);
      }

      this.isLoading = false;
      this.resetForm();
    }, 1000);
  }

  resetForm() {
    this.formData = {
      mode: 'add',
      file: null,
      preview: '',
      description: '',
      documentId: null,
    };
  }

  getFileType(file: File): string {
    const ext = file.name.split('.').pop()?.toUpperCase();
    return ext ?? 'UNKNOWN';
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-success';
      case 'pending':
        return 'bg-warning text-dark';
      case 'rejected':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  }

  getFileTypeColor(type: string): string {
    switch (type.toUpperCase()) {
      case 'PDF':
        return 'danger';
      case 'DOC':
      case 'DOCX':
        return 'primary';
      case 'XLS':
      case 'XLSX':
        return 'success';
      default:
        return 'secondary';
    }
  }

  getTranslatedStatus(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return this.translate.instant('PROFILE.REGISTRATION_PAPERS.STATUS.APPROVED');
      case 'pending':
        return this.translate.instant('PROFILE.REGISTRATION_PAPERS.STATUS.PENDING');
      case 'rejected':
        return this.translate.instant('PROFILE.REGISTRATION_PAPERS.STATUS.REJECTED');
      default:
        return status;
    }
  }
}
