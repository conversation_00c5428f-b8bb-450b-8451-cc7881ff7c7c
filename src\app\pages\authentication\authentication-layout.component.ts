import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../modules/i18n/translation.service';

@Component({
  selector: 'app-authentication-layout',
  templateUrl: './authentication-layout.component.html',
  styleUrls: ['./authentication-layout.component.scss']
})
export class AuthenticationLayoutComponent implements OnInit {

  constructor(
    private translateService: TranslateService,
    private translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();
    console.log('Authentication Layout - Current language:', currentLang);
    console.log('Authentication Layout - localStorage language:', localStorage.getItem('language'));

    // Set the language in TranslateService
    this.translateService.use(currentLang);
    console.log('Authentication Layout - TranslateService current lang:', this.translateService.currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
    console.log('Authentication Layout - Direction applied for:', currentLang);
  }

}
