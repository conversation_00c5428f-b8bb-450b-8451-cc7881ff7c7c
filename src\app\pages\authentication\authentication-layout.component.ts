import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../modules/i18n/translation.service';

@Component({
  selector: 'app-authentication-layout',
  templateUrl: './authentication-layout.component.html',
  styleUrls: ['./authentication-layout.component.scss']
})
export class AuthenticationLayoutComponent implements OnInit {

  constructor(
    private translateService: TranslateService,
    private translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();

    // Set the language in TranslateService
    this.translateService.use(currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
  }

}
