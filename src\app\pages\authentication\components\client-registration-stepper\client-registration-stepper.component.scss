// Required field indicator
.required {
  color: #dc3545; // Bootstrap danger color
  font-weight: bold;
  margin-left: 2px;
}

// RTL Support
:host-context(.rtl) {
  .required {
    margin-left: 0;
    margin-right: 2px;
  }

  .client-registration-stepper {
    direction: rtl;
    text-align: right;
  }

  .form-group {
    text-align: right !important;

    .form-label {
      justify-content: flex-start !important;
      text-align: right;
      direction: rtl;

      // عكس Bootstrap margins في RTL
      .me-2 {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }

      .ms-1 {
        margin-left: 0 !important;
        margin-right: 0.25rem !important;
      }
    }

    label {
      justify-content: flex-start !important;
      text-align: right;
      direction: rtl;

      // عكس Bootstrap margins في RTL
      .me-2 {
        margin-right: 0 !important;
        margin-left: 0.5rem !important;
      }

      .ms-1 {
        margin-left: 0 !important;
        margin-right: 0.25rem !important;
      }
    }
  }

  .form-check {
    text-align: right;
    direction: rtl;

    .form-check-input {
      margin-left: 0.5rem;
      margin-right: 0;
    }

    .form-check-label {
      text-align: right;
    }
  }
}

.client-registration-stepper {
  width: 100%;
  direction: ltr;
  text-align: left;

  .stepper-header {
    margin-bottom: 10px;
    text-align: center;

    .stepper-title {
      color: #333;
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 15px;
    }

    .stepper-progress {
      .progress-bar {
        width: 100%;
        height: 6px;
        background-color: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 10px;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }

      .d-flex {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        margin-top: 5px;

        .progress-text {
          color: #666;
          font-size: 0.8rem;
          font-weight: 500;
          margin: 0;
        }

        .back-to-previous {
          background: none;
          border: none;
          color: #007bff;
          font-size: 0.85rem;
          font-weight: 500;
          cursor: pointer;
          padding: 3px 0;
          text-decoration: none;
          transition: color 0.3s ease;
          white-space: nowrap;

          &:hover {
            color: #0056b3;
            text-decoration: underline;
          }

          &:focus {
            outline: none;
          }
        }
      }
    }
  }

  .stepper-form {
    .step-content {
      min-height: 50px;
      margin-bottom: 5px;
      text-align: center;
      padding: 2px 5px;

      &.success-step {
        min-height: 50px;
        padding: 5px;
      }

      .step-title {
        color: #232176;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 5px;
        text-align: center;
        padding-bottom: 3px;

      }

      .row {
        margin: 0 -10px;

        .col-md-6 {
          padding: 0 10px;
        }
      }

      .form-group {
        margin-bottom: 8px;

        label {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 6px;
          font-weight: 500;
          color: #333;
          font-size: 0.9rem;
          text-align: left;

          i {
            color: #28a745;
            font-size: 1rem;
          }
        }

        .form-control {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 14px;
          transition: all 0.3s ease;
          background-color: #fff;

          &:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
          }

          &.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
          }

          &::placeholder {
            color: #999;
          }
        }

        .invalid-feedback {
          display: block;
          color: #dc3545;
          font-size: 12px;
          margin-top: 5px;
          font-weight: 500;
          text-align: left;
          animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-5px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .form-help-text {
          margin-top: 3px;
          text-align: left;

          small {
            color: #6c757d;
            font-size: 11px;
            line-height: 1.3;
          }
        }
      }

      // Gender Selection Styles
      .gender-selection {
        margin-bottom: 8px;

        .gender-buttons {
          display: flex;
          gap: 8px;
          justify-content: center;
          margin-bottom: 8px;

          .gender-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            background: #f8f9fa;
            color: #6c757d;
            font-weight: 500;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            justify-content: center;

            i {
              font-size: 18px;
            }

            &:hover {
              border-color: #007bff;
              background: #e7f3ff;
              color: #007bff;
            }

            &.selected {
              border-color: #007bff;
              background: #007bff;
              color: white;
            }

            &.female.selected {
              border-color: #e91e63;
              background: #e91e63;
            }

            &.female:hover {
              border-color: #e91e63;
              background: #fce4ec;
              color: #e91e63;
            }
          }
        }
      }

      // Verification Code Styles
      .verification-code-section {
        margin: 15px 0;
        text-align: center;
        padding: 0 5px;

        .verification-inputs {
          display: flex;
          justify-content: center;
          gap: 10px;
          margin-bottom: 12px;

          .code-input {
            .verification-input {
              width: 50px;
              height: 50px;
              text-align: center;
              font-size: 1.5rem;
              font-weight: bold;
              border: 2px solid #ddd;
              border-radius: 8px;
              background-color: #fff;
              color: #333;
              transition: all 0.3s ease;

              &:focus {
                outline: none;
                border-color: #28a745;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
              }

              &::-webkit-outer-spin-button,
              &::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }

              &[type="number"] {
                -moz-appearance: textfield;
              }
            }
          }
        }
      }

      // Countdown Section
      .countdown-section {
        text-align: center;
        margin-bottom: 12px;

        .countdown-text {
          color: #6c757d;
          font-size: 13px;

          .countdown-timer {
            color: #007bff;
            font-weight: bold;
          }
        }
      }

      // Progress Bar for Step 3
      .progress-bar-container {
        margin: 20px 0;

        .progress-bar {
          display: flex;
          gap: 5px;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;

          .progress-fill {
            flex: 1;
            background: #28a745;
            border-radius: 2px;

            &.step-1 {
              background: #28a745;
            }
            &.step-2 {
              background: #28a745;
            }
            &.step-3 {
              background: #28a745;
            }
            &.step-4 {
              background: #28a745;
            }
          }
        }
      }

      .form-check {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-top: 12px;

        .form-check-input {
          margin: 0;
          margin-top: 3px;
          width: 18px;
          height: 18px;
          cursor: pointer;

          &.is-invalid {
            border-color: #dc3545;
          }
        }

        .form-check-label {
          margin: 0;
          font-size: 14px;
          color: #333;
          cursor: pointer;
          line-height: 1.4;
          text-align: left;
        }

        .terms-link {
          color: #28a745;
          text-decoration: none;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .terms-content {
        .welcome-message {
          text-align: center;
          margin-bottom: 30px;
          padding: 20px;
          background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
          border-radius: 12px;
          border: 1px solid #d4edda;

          i {
            font-size: 3rem;
            color: #28a745;
            margin-bottom: 15px;
          }

          h4 {
            color: #155724;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
          }

          p {
            color: #155724;
            font-size: 0.95rem;
            line-height: 1.5;
            margin: 0;
          }
        }

        .client-benefits {
          margin-top: 25px;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;

          h5 {
            color: #333;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
          }

          ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-bottom: 10px;
              font-size: 0.9rem;
              color: #555;

              i {
                color: #28a745;
                font-size: 1rem;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    .stepper-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 15px;
      margin-top: 30px;
      flex-direction: row;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;

        i {
          font-size: 1rem;
        }

        &.btn-secondary {
          background-color: #6c757d;
          color: white;

          &:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
          }
        }

        &.btn-primary {
          background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
          color: white;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
          }
        }

        &.btn-success {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          color: white;

          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
          }

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }

    // Success Step Styles
    .success-step {
      text-align: center;
      padding: 10px 8px;

      .success-content {
        max-width: 320px;
        margin: 0 auto;

        .success-icon {
          margin-bottom: 8px;

          i {
            font-size: 2rem;
            color: #28a745;
          }
        }

        .success-title {
          color: #333;
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 8px;
          direction: ltr;
          text-align: center;
        }

        .success-message {
          color: #666;
          font-size: 0.8rem;
          line-height: 1.3;
          margin-bottom: 12px;
          direction: ltr;
          text-align: center;
        }

        .success-illustration {
          margin: 10px 0;

          .success-image {
            max-width: 180px;
            width: 100%;
            height: auto;
          }
        }

        .btn-success-action {
          background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);
          border: none;
          color: white;
          padding: 10px 25px;
          font-size: 13px;
          font-weight: 600;
          border-radius: 25px;
          margin: 10px auto;
          transition: all 0.3s ease;
          direction: ltr;
          display: block;
          width: fit-content;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);
          }
        }

        .additional-info {
          .info-link {
            color: #28a745;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            direction: ltr;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  .alert {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid transparent;

    &.alert-danger {
      background-color: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
    }
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  .text-success {
    color: #28a745 !important;
  }

  // Additional Styles for New Design
  .btn-verification {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 20px;
    margin: 8px auto 6px auto;
    background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
    display: block;
    text-align: center;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      background: #6c757d !important;
      box-shadow: none !important;

      &:hover {
        transform: none !important;
        box-shadow: none !important;
      }
    }
  }

  // Help Text
  .help-text {
    text-align: center;
    margin-top: 8px;
    color: #6c757d;
    font-size: 12px;

    .contact-link {
      color: #007bff;
      cursor: pointer;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .client-registration-stepper {
    .stepper-header {
      .stepper-title {
        font-size: 1.3rem;
      }
    }

    .stepper-form {
      .step-content {
        min-height: auto;

        .row {
          .col-md-6 {
            margin-bottom: 15px;
          }
        }

        // Gender buttons responsive
        .gender-selection {
          .gender-buttons {
            flex-direction: column;
            gap: 10px;

            .gender-btn {
              min-width: auto;
              padding: 12px 20px;
              font-size: 14px;
            }
          }
        }

        // Verification code responsive
        .verification-code-section {
          .verification-inputs {
            gap: 10px;

            .code-input {
              .verification-input {
                width: 50px;
                height: 50px;
                font-size: 20px;
              }
            }
          }
        }

        .terms-content {
          .welcome-message {
            padding: 15px;

            i {
              font-size: 2.5rem;
            }

            h4 {
              font-size: 1.1rem;
            }

            p {
              font-size: 0.9rem;
            }
          }

          .client-benefits {
            padding: 15px;

            h5 {
              font-size: 1rem;
            }

            ul li {
              font-size: 0.85rem;
            }
          }
        }
      }

      .stepper-navigation {
        flex-direction: column;
        gap: 10px;

        .btn {
          width: 100%;
          min-width: auto;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .client-registration-stepper {
    .stepper-header {
      margin-bottom: 20px;

      .stepper-title {
        font-size: 1.1rem;
      }

      .stepper-progress {
        .progress-text {
          font-size: 0.8rem;
        }
      }
    }

    .stepper-form {
      .step-content {
        .step-title {
          font-size: 1rem;
        }

        .form-group {
          label {
            font-size: 0.85rem;
          }

          .form-control {
            padding: 10px 12px;
            font-size: 13px;
          }
        }

        // Gender buttons for small screens
        .gender-selection {
          .gender-buttons {
            .gender-btn {
              padding: 10px 15px;
              font-size: 13px;
              min-width: auto;

              i {
                font-size: 16px;
              }
            }
          }
        }

        // Verification code for small screens
        .verification-code-section {
          .verification-inputs {
            gap: 8px;

            .code-input {
              .verification-input {
                width: 45px;
                height: 45px;
                font-size: 18px;
              }
            }
          }
        }
      }
    }
  }
}
