<!-- header -->
<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-9 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="me-7 mb-4">
        <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
          <img [src]="user.image" alt="Profile Image" *ngIf="user.image" />

          <div class="position-absolute bottom-0 start-50 translate-middle-x mb-2">
            <label
              class="btn btn-sm btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow d-flex align-items-center justify-content-center"
              for="profile-image-input" data-bs-toggle="tooltip" [title]="'PROFILE.CHANGE_PROFILE_PICTURE' | translate">
              <i class="bi bi-camera-fill fs-7 text-primary">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
            </label>
            <input type="file" id="profile-image-input" class="d-none" accept=".png, .jpg, .jpeg"
              (change)="onProfileImageChange($event)" *ngIf="
                user.role === 'broker' ||
                user.role === 'client' ||
                user.role === 'developer'||
                user.role === 'admin'
              " />
          </div>
        </div>
      </div>

      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex flex-column flex-grow-1">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-800 fs-2 fw-bolder me-1 cursor-pointer">{{ capitalizeWords(user.fullName) }}
              </span>
              <span class="badge fw-bolder ms-2 fs-6 py-1 px-3" *ngIf="user.role === 'broker'"
                [ngClass]="getAccountTypeBadge(user.accountType)">
                {{ capitalizeWords(user?.accountType) }}
              </span>
            </div>

            <div class="d-flex flex-wrap fw-bold fs-6 mb-4 pe-2">
              <a class="d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer">
                <app-keenicon name="phone" class="fs-4 me-1 text-mid-blue"></app-keenicon>
                {{ user.phone }}
              </a>
              <a class="d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer">
                <app-keenicon name="sms" class="fs-4 me-1 text-mid-blue"></app-keenicon>
                {{ user.email }}
              </a>
              <a class="d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer">
                <app-keenicon name="user" class="fs-4 me-1 text-mid-blue"></app-keenicon>
                {{ capitalizeWords(user.role) }}
              </a>
            </div>
          </div>

          <div class="d-flex my-4 align-self-center" *ngIf="user.role === 'broker'">
            <a class="btn btn-md btn-dark-blue btn-active-light-dark-blue cursor-pointer" data-bs-toggle="modal"
              data-bs-target="#kt_modal_offer_a_deal">
              {{ 'PROFILE.UPGRADE_PLAN' | translate }}
            </a>
          </div>
        </div>

        <div class="d-flex flex-wrap flex-stack" *ngIf="user.role === 'broker'">
          <div class="d-flex flex-column flex-grow-1 pe-8">
            <div class="d-flex flex-wrap">
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                  <div class="fs-2 fw-bolder text-center w-100">
                    {{ user.specializationsCount | arabicNumbers }}
                  </div>
                </div>
                <div class="fw-bold fs-6 text-gray-500 text-center">{{ 'PROFILE.SPECIALIZATIONS' | translate }}</div>
              </div>

              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                  <div class="fs-2 fw-bolder text-center w-100">{{ (user.areasCount || 0) | arabicNumbers }}</div>
                </div>
                <div class="fw-bold fs-6 text-gray-500 text-center">{{ 'PROFILE.LOCATIONS' | translate }}</div>
              </div>

              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                  <div class="fs-2 fw-bolder text-center w-100">{{ user.operationCount | arabicNumbers }}</div>
                </div>
                <div class="fw-bold fs-6 text-gray-500 text-center">{{ 'PROFILE.OPERATIONS' | translate }}</div>
              </div>

              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                  <div class="fs-2 fw-bolder text-center w-100">
                    {{ user.specializationAdvertisementCount | arabicNumbers }}
                  </div>
                </div>
                <div class="fw-bold fs-6 text-gray-500 text-center">{{ 'PROFILE.ADVERTISEMENTS' | translate }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>