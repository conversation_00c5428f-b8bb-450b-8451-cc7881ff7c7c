// Cursor pointer for clickable elements
.cursor-pointer {
  cursor: pointer;
}

// Status badge styling
.badge-light-success {
  color: #50cd89;
  background-color: #e8fff3;
}

.badge-light-warning {
  color: #ffc700;
  background-color: #fff8dd;
}

.badge-light-danger {
  color: #f1416c;
  background-color: #fff5f8;
}

// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;

    .card-title {
      width: 100%;
      text-align: right;
      justify-content: flex-end;
      display: flex;

      h3 {
        text-align: right;
        margin-left: auto;
        margin-right: 0;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
      }
    }

    .card-toolbar {
      order: -1;
      margin-left: 0;
      margin-right: auto;
    }
  }

  .card-body {
    text-align: right;
    direction: rtl;

    .row {
      direction: rtl;
    }

    .col-md-4,
    .col-sm-6 {
      text-align: right;
    }

    .card {
      text-align: center;

      .card-body {
        text-align: center;
        position: relative;

        .badge {
          left: 0.5rem;
          right: auto;
        }

        .btn {
          margin-left: 0;
          margin-right: 0;
        }

        .text-muted {
          text-align: center;
        }
      }
    }

    // Empty state
    .text-center {
      text-align: center !important;

      h5, p {
        text-align: center !important;
      }
    }
  }

  .card-footer {
    text-align: left;
    justify-content: flex-start;

    .btn {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  // Modal RTL
  .modal-header {
    text-align: right;

    .modal-title {
      text-align: right;
    }

    .btn-close {
      margin-left: auto;
      margin-right: 0;
    }
  }

  .modal-body {
    text-align: right;
    direction: rtl;

    .form-label {
      text-align: right;
    }

    .form-control {
      text-align: right;
    }

    .alert {
      text-align: right;

      .fa {
        margin-left: 0.25rem;
        margin-right: 0;
      }
    }

    .text-muted {
      text-align: right;

      .fa {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }

  .modal-footer {
    text-align: left;
    justify-content: flex-start;

    .btn {
      margin-left: 0;
      margin-right: 0.5rem;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
