// Profile Component Styles

.profile-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  flex-direction: column;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3rem solid #f3f3f3;
  border-top: 0.3rem solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.1rem;
  color: #6c757d;
}

.profile-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .profile-content {
    padding: 0 10px;
  }

  .card {
    margin-bottom: 1rem;
  }

  .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .profile-content {
    padding: 0 5px;
  }

  .card-body {
    padding: 0.75rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

// Ensure proper spacing
.card {
  margin-bottom: 1.5rem;
}

.card:last-child {
  margin-bottom: 0;
}

// RTL Support
:host-context(.rtl) {
  direction: rtl;
  text-align: right;

  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;
  }

  .card-body {
    text-align: right;
  }

  .form-label {
    text-align: right;
  }

  .form-control {
    text-align: right;
  }

  .btn {
    margin-left: 0;
    margin-right: 0.5rem;
  }
}
