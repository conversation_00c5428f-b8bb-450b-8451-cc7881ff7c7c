// Simple Loading Styles
.profile-loading-container {
  min-height: 100vh;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
    border-color: #007bff;
    border-right-color: transparent;
  }

  .loading-text {
    color: #495057;
    font-size: 1rem;
    margin-top: 1rem;
    font-weight: 400;
  }
}

// Simple fade in for content
.profile-content {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// ستايلات عامة لملء الصفحة
:host {
  display: block;
  width: 100%;
  min-height: 100vh;
}

// ستايلات عامة للمحتوى
.profile-content {
  width: 100%;

  .container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .row {
    width: 100% !important;
    margin: 0 !important;
  }

  .col-12 {
    width: 100% !important;
    padding: 0 15px !important;
  }
}

// تحسين العرض في جميع الأحجام
.profile-content {
  width: 100%;
  max-width: 100%;
  padding: 0 15px;

  @media (min-width: 1200px) {
    padding: 0 30px;
  }
}

// RTL Support - حل محسن بدون scroll bar
:host-context(.rtl) {
  // إجبار المكون على أخذ العرض الكامل بطريقة responsive
  display: block !important;
  width: 100% !important; // استخدام 100% بدلاً من calc
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  overflow-x: hidden !important; // منع الـ scroll الأفقي


  .profile-loading-container {
    direction: rtl;
    text-align: right;
    width: 100vw !important;
    max-width: 100vw !important;
  }

  .loading-text {
    text-align: center;
  }

  .profile-content {
    direction: rtl;
    text-align: right;
    width: 100% !important; // استخدام 100% بدلاً من 100vw
    max-width: 100% !important;
    min-height: 100vh;
    padding: 0 15px !important; // إضافة padding للمحتوى
    margin: 0 !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important; // منع الـ scroll الأفقي
  }

  // إزالة أي containers أو wrappers تحد من العرض
  .container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl {
    width: 100% !important; // استخدام 100% بدلاً من 100vw
    max-width: 100% !important;
    padding-left: 15px !important; // إضافة padding مناسب
    padding-right: 15px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .col, .col-12, .col-lg-8, .col-lg-4, .col-md-6, .col-sm-12 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  // جعل البطاقات تأخذ العرض الكامل
  .card {
    width: 100% !important;
    margin-bottom: 1.5rem !important;
  }

  // تحسين المسافات في العربية
  .mb-5, .mb-xl-10 {
    margin-bottom: 1rem !important;
  }

  // جعل المحتوى يتمدد بالكامل
  .profile-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    > * {
      flex-grow: 1;
    }
  }

  // إزالة أي margins أو paddings من الـ body أو html في العربية
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100vw !important;
    overflow-x: hidden !important;
  }

  // حل خاص للـ body في العربية لاستغلال المساحة الفارغة
  body {
    &.rtl {
      .main-content, .content-wrapper, .page-wrapper {
        margin-right: -250px !important;
        padding-right: 250px !important;
        width: calc(100vw + 250px) !important;
        max-width: calc(100vw + 250px) !important;
      }
    }
  }

  // التأكد من أن المكون الرئيسي يأخذ العرض الكامل
  app-root {
    width: 100vw !important;
    max-width: 100vw !important;
    display: block !important;
  }

  // إزالة أي wrappers تحد من العرض
  .main-wrapper, .content-wrapper, .page-wrapper {
    width: 100vw !important;
    max-width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  // حل خاص للـ main content area في العربية
  .main-content, .content-area, .page-content {
    width: 100vw !important;
    max-width: 100vw !important;
    margin-right: -250px !important; // تعويض عرض الـ sidebar
    padding-right: 250px !important; // إضافة padding لتجنب تداخل المحتوى مع الـ sidebar
    box-sizing: border-box !important;
  }

  // حل نهائي للمساحة الفارغة في العربية - responsive
  .profile-content {
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;

    .container-fluid {
      position: relative !important;
      left: 0 !important;
      right: 0 !important;
      transform: none !important;
      width: 100% !important;
      max-width: 100% !important;
      padding-left: 15px !important;
      padding-right: 15px !important;
    }

    .col-12 {
      padding-left: 15px !important;
      padding-right: 15px !important;
    }
  }

  // إجبار جميع العناصر الفرعية على أخذ العرض الكامل
  app-profile-header,
  app-profile-details,
  app-sign-in-method,
  app-account-details,
  app-advertisements-details {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}
