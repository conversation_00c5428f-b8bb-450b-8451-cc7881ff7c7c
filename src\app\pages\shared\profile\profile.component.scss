// Simple Loading Styles
.profile-loading-container {
  min-height: 100vh;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
    border-color: #007bff;
    border-right-color: transparent;
  }

  .loading-text {
    color: #495057;
    font-size: 1rem;
    margin-top: 1rem;
    font-weight: 400;
  }
}

// Simple fade in for content
.profile-content {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// ستايلات عامة لملء الصفحة
:host {
  display: block;
  width: 100%;
  min-height: 100vh;
}

// ستايلات عامة للمحتوى
.profile-content {
  width: 100%;

  .container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .row {
    width: 100% !important;
    margin: 0 !important;
  }

  .col-12 {
    width: 100% !important;
    padding: 0 15px !important;
  }
}

// تحسين العرض في جميع الأحجام
.profile-content {
  width: 100%;
  max-width: 100%;
  padding: 0 15px;

  @media (min-width: 1200px) {
    padding: 0 30px;
  }
}

// RTL Support - Simple and clean
:host-context(.rtl) {
  direction: rtl;
  text-align: right;

  .profile-content {
    direction: rtl;
    text-align: right;
  }

  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;
  }

  .card-body {
    text-align: right;
  }

  .form-label {
    text-align: right;
  }

  .form-control {
    text-align: right;
  }



}
