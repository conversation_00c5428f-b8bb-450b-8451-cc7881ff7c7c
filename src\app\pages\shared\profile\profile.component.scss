// Simple Loading Styles
.profile-loading-container {
  min-height: 100vh;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
    border-color: #007bff;
    border-right-color: transparent;
  }

  .loading-text {
    color: #495057;
    font-size: 1rem;
    margin-top: 1rem;
    font-weight: 400;
  }
}

// Simple fade in for content
.profile-content {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// ستايلات عامة لملء الصفحة
:host {
  display: block;
  width: 100%;
  min-height: 100vh;
}

// تحسين العرض في جميع الأحجام
.profile-content {
  width: 100%;
  max-width: 100%;
  padding: 0 15px;

  @media (min-width: 1200px) {
    padding: 0 30px;
  }
}

// RTL Support
:host-context(.rtl) {
  .profile-loading-container {
    direction: rtl;
    text-align: right;
  }

  .loading-text {
    text-align: center;
  }

  .profile-content {
    direction: rtl;
    text-align: right;
    width: 100%;
    min-height: 100vh;
    padding: 0;
    margin: 0;
  }

  // جعل المحتوى يأخذ الصفحة كاملة في العربية
  :host {
    display: block;
    width: 100%;
    min-height: 100vh;
  }

  .container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
  }

  .col, .col-12, .col-lg-8, .col-lg-4 {
    padding-left: 8px !important;
    padding-right: 8px !important;
    width: 100% !important;
  }

  // جعل البطاقات تأخذ العرض الكامل
  .card {
    width: 100% !important;
    margin-bottom: 1.5rem !important;
  }

  // تحسين المسافات في العربية
  .mb-5, .mb-xl-10 {
    margin-bottom: 1rem !important;
  }

  // جعل المحتوى يتمدد بالكامل
  .profile-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    > * {
      flex-grow: 1;
    }
  }
}
