// Badge with delete button styles
.badge {
  padding: 0.5rem 1rem !important;
  position: relative;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;

  .btn {
    width: 15px;
    height: 15px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    opacity: 0;
    transition: all 0.2s ease;
    box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.1);

    i {
      font-size: 8px;
    }

    &:hover {
      background-color: #e6dadb !important;
      color: #ffffff !important;
    }
  }

  &:hover {
    .btn {
      opacity: 1;
    }
  }
}

// Modal styles
.modal-content {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  border-radius: 0.475rem;

  .modal-header {
    background-color: #f1f8ff;
    border-bottom: 1px solid #eff2f5;

    .modal-title {
      color: #181c32;
    }
  }

  .modal-body {
    padding: 2rem;

    .form-control-solid,
    .form-select-solid {
      background-color: #f5f8fa;
      border-color: #f5f8fa;
      color: #5e6278;
      transition: color 0.2s ease, background-color 0.2s ease;

      &:focus {
        background-color: #eef3f7;
        border-color: #eef3f7;
      }
    }

    .form-text {
      font-size: 0.85rem;
    }
  }

  .modal-footer {
    border-top: 1px solid #eff2f5;
    padding: 1.5rem;
  }
}

// Specialization tree styles
.specialization-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eff2f5;
  border-radius: 0.475rem;
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.05);
}

.specialization-tree {
  .tree-node {
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    border-radius: 0.475rem;

    &:hover {
      background-color: #f8f9fa;
    }

    .node-name {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: #009ef7;
      }
    }
  }

  .tree-node-type {
    transition: all 0.2s ease;
    margin-bottom: 0.25rem;
    border-left: 2px solid #eff2f5;

    &:hover {
      background-color: #f8f9fa;
    }
  }

  .form-check-input {
    cursor: pointer;

    &:checked {
      background-color: #009ef7;
      border-color: #1ac228;
    }
  }

  .btn-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f1f8ff;
      color: #009ef7;
    }

    i {
      font-size: 0.8rem;
    }
  }
}

// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .badge {
    .btn {
      margin-left: 0;
      margin-right: 5px;
    }
  }

  .modal-content {
    direction: rtl;
    text-align: right;

    .modal-header {
      text-align: right;
    }

    .modal-body {
      text-align: right;

      .form-control, .form-select {
        text-align: right;
      }

      .dropdown-menu {
        text-align: right;
      }
    }

    .modal-footer {
      text-align: left;

      .btn {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }

  .specialization-tree {
    .tree-node {
      text-align: right;

      .d-flex {
        flex-direction: row-reverse;
      }
    }

    .tree-node-type {
      border-left: none;
      border-right: 2px solid #eff2f5;
      padding-right: 1rem;
      padding-left: 0;
    }

    .me-2, .me-5 {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
    }

    .ms-3 {
      margin-left: 0 !important;
      margin-right: 0.75rem !important;
    }
  }

  .text-end {
    text-align: left !important;
  }

  .d-flex {
    flex-direction: row-reverse;

    &.justify-content-between {
      flex-direction: row-reverse;
    }
  }
}
