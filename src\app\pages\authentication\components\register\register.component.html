<div class="register-container">
  <!-- Welcome Header -->
  <!-- <div class="welcome-header">
    <h1 class="welcome-title">مرحباً بك في إيزي ديل</h1>
  </div> -->

  <!-- Registration Form Card -->
  <div class="register-card">
    <!-- Step 1: User Type Selection -->
    <div *ngIf="currentStep === 1" class="user-type-selection">
      <h2 class="form-title">{{ 'AUTH.REGISTER.CHOOSE_ACCOUNT_TYPE' | translate }}</h2>
      <p class="form-subtitle">
        {{ 'AUTH.REGISTER.CHOOSE_ACCOUNT_DESC' | translate }}
      </p>

      <!-- User Type Options -->
      <div class="user-type-options">
        <div class="user-type-option" [class.selected]="selectedUserType === 'client'"
          (click)="selectUserType('client')">
          <span class="option-label">{{ 'AUTH.REGISTER.CLIENT' | translate }}</span>
          <div class="option-icon">
            <i class="ki-outline ki-user"></i>
          </div>
        </div>

        <div class="user-type-option" [class.selected]="selectedUserType === 'broker'"
          (click)="selectUserType('broker')">
          <span class="option-label">{{ 'AUTH.REGISTER.BROKER' | translate }}</span>
          <div class="option-icon">
            <i class="ki-outline ki-people"></i>
          </div>
        </div>

        <div class="user-type-option" [class.selected]="selectedUserType === 'developer'"
          (click)="selectUserType('developer')">
          <span class="option-label">{{ 'AUTH.REGISTER.DEVELOPER' | translate }}</span>
          <div class="option-icon">
            <i class="ki-outline ki-home"></i>
          </div>
        </div>
      </div>

      <!-- Continue Button -->
      <button class="continue-btn" [disabled]="!selectedUserType" (click)="nextStep()">
        {{ 'AUTH.REGISTER.CHOOSE' | translate }}
      </button>

      <!-- Login Link -->
      <div routerLink="../login" class="login-link-container cursor-pointer">
        <a class="login-link">
          <i class="ki-outline ki-arrow-right fs-3"></i>
        </a>
        <span class="login-text">{{ 'AUTH.GENERAL.ALREADY_HAVE_ACCOUNT' | translate }} {{ 'AUTH.GENERAL.GO_TO_LOGIN' |
          translate }}</span>
      </div>

      <!-- Support Link -->
      <div class="support-link">
        <span class="support-text">{{ 'AUTH.GENERAL.NEED_HELP' | translate }} {{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 2: Registration Stepper -->
    <div *ngIf="currentStep === 2" class="registration-stepper-step">
      <!-- Client Registration Stepper -->
      <app-client-registration-stepper *ngIf="selectedUserType === 'client'" (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)">
      </app-client-registration-stepper>

      <!-- Broker Registration Stepper -->
      <app-broker-registration-stepper *ngIf="selectedUserType === 'broker'" (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)">
      </app-broker-registration-stepper>

      <!-- Developer Registration Stepper -->
      <app-developer-registration-stepper *ngIf="selectedUserType === 'developer'" (onBack)="previousStep()"
        (onComplete)="onRegistrationComplete($event)">
      </app-developer-registration-stepper>
    </div>
  </div>
</div>