<!-- Account details -->
<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 bg-light-dark-blue" role="button" data-bs-toggle="collapse"
    data-bs-target="#kt_account_ads_details" aria-expanded="true" aria-controls="kt_account_ads_details">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{ 'PROFILE.ACCOUNT_DETAILS.TITLE' | translate }}</h3>
    </div>
  </div>
  <div id="kt_account_ads_details" class="collapse show">
    <form novalidate="" class="form">
      <div class="card-body border-top p-9">
        <!-- <div class="row">
          <label class="col-lg-4 col-form-label required fw-bold fs-6"
            >Account type</label
          >
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9">
                <div class="d-flex flex-wrap fw-bold fs-6 mb-4 pe-2">
                  <span
                    *ngFor="let spec of specializations"
                    class="d-flex align-items-center me-5 mb-2 fw-bolder py-4 px-5 fs-6 badge badge-warning text-white"
                  >
                    {{ spec }}
                  </span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button
                    class="btn btn-light-dark-blue btn-sm"
                    (click)="navigateToDetails()"
                  >
                    <i class="fa-solid fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <!--  -->
        <div class="row mb-6">
          <label class="col-lg-4 col-form-label required fw-bold fs-6">{{ 'PROFILE.ACCOUNT_DETAILS.REGISTRATION_PAPERS'
            | translate }}</label>
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9"></div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button class="btn btn-light-dark-blue btn-sm" (click)="navigateToRegistrationPapers()">
                    <i class="fa-solid fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--  -->
        <div class="row mb-6">
          <label class="col-lg-4 col-form-label required fw-bold fs-6">{{ 'PROFILE.ACCOUNT_DETAILS.PAYMENTS_RENEWALS' |
            translate }}</label>
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9"></div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button class="btn btn-light-dark-blue btn-sm">
                    <i class="fa-solid fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--  -->
        <!-- <div class="row mb-6">
          <label class="col-lg-4 col-form-label required fw-bold fs-6"
            >Verification</label
          >
          <div class="col-lg-8 fv-row">
            <div class="row">
              <div class="col-md-9"></div>
              <div class="col-md-3">
                <div class="align-items-end text-end">
                  <button
                    class="btn btn-light-dark-blue btn-sm"
                    (click)="navigateToDetails()"
                  >
                    <i class="fa-solid fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </form>
  </div>
</div>