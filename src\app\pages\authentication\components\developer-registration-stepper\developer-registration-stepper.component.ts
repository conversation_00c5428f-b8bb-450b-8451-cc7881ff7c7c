import {
  Component,
  Output,
  EventEmitter,
  ChangeDetectorRef, OnInit,
} from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DeveloperRegistrationData } from '../../models';
import { AuthenticationService } from '../../services/authentication.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../../modules/i18n/translation.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-developer-registration-stepper',
  templateUrl: './developer-registration-stepper.component.html',
  styleUrls: ['./developer-registration-stepper.component.scss'],
})
export class DeveloperRegistrationStepperComponent implements OnInit {
  @Output() onBack = new EventEmitter<void>();
  @Output() onComplete = new EventEmitter<DeveloperRegistrationData>();

  registrationForm: FormGroup;
  currentStep = 1;
  totalSteps = 5;
  isLoading = false;
  isLoadingSendOtp: boolean = false;
  isLoadingCheckOtp: boolean = false;
  isLoadingCreateAccount: boolean = false;
  otpErrorMessage: string = '';
  uploadedFiles: { [key: string]: File[] } = {};
  verificationDigits: string[] = ['', '', '', '', ''];
  countdown: number = 30;
  showResendButton: boolean = false;
  showPassword: boolean = false;
  showConfirmPassword: boolean = false;

  // Validators
  static noNumbers = Validators.pattern(/^[^0-9]*$/);
  static emailOrPhonePattern = Validators.pattern(/^([^\s@]+@[^\s@]+\.[^\s@]+|01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);

  private static isEmail(value: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
  }

  constructor(
    private fb: FormBuilder,
    private authenticationService: AuthenticationService,
    private cd: ChangeDetectorRef,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    this.registrationForm = this.createForm();
  }

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();

    // Set the language in TranslateService
    this.translateService.use(currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Step 1: Company Name, Email/Phone
      fullName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          DeveloperRegistrationStepperComponent.noNumbers,
        ],
      ],
      email_phone: [
        '',
        [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern],
      ],

      // Step 2: Verification Code
      verificationCode: this.fb.array(
        Array(5)
          .fill('')
          .map(() =>
            this.fb.control('', [
              Validators.required,
              Validators.pattern('[0-9]'),
            ])
          )
      ),

      // Step 4: Phone, Email, Password, Terms
      phone: ['', [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern]],
      email: ['', [Validators.email]],
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
        ],
      ],
      password_confirmation: ['', [Validators.required]],
      agreeTerms: [false, [Validators.requiredTrue]],
    });
  }

  // Helper methods for validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.registrationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  markFieldAsTouched(fieldName: string): void {
    this.registrationForm.get(fieldName)?.markAsTouched();
  }

  getFieldError(fieldName: string): string {
    const field = this.registrationForm.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;
    if (errors['required']) return 'This field is required';
    if (errors['pattern'] && fieldName === 'fullName') return 'Company name cannot contain numbers';
    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';
    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';
    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';
    if (errors['email']) return 'Enter valid email';
    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;

    return 'Invalid input';
  }

  getFormError(): string {
    const password = this.registrationForm.get('password')?.value;
    const confirmPassword = this.registrationForm.get(
      'password_confirmation'
    )?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      return 'Passwords do not match';
    }
    return '';
  }

  // Check if step is valid
  isStep1Valid(): boolean {
    const fullName = this.registrationForm.get('fullName');
    const emailPhone = this.registrationForm.get('email_phone');

    return !!(fullName?.valid && emailPhone?.valid);
  }

  isStep2Valid(): boolean {
    const verificationCode = this.registrationForm.get(
      'verificationCode'
    ) as FormArray;
    return verificationCode.valid;
  }

  isStep4Valid(): boolean {
    const phone = this.registrationForm.get('phone');
    const email = this.registrationForm.get('email');
    const password = this.registrationForm.get('password');
    const passwordConfirmation = this.registrationForm.get(
      'password_confirmation'
    );
    const agreeTerms = this.registrationForm.get('agreeTerms');

    // Check if passwords match
    const passwordsMatch = password?.value === passwordConfirmation?.value;

    return !!(
      phone?.valid &&
      email?.valid &&
      password?.valid &&
      passwordConfirmation?.valid &&
      agreeTerms?.valid &&
      passwordsMatch
    );
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.onBack.emit();
    }
  }

  get verificationCodeControls() {
    return (this.registrationForm.get('verificationCode') as FormArray)
      .controls;
  }

  onDigitInput(index: number): void {
    const code = this.verificationDigits.join('');
    this.registrationForm.patchValue({ verificationCode: code });
  }

  handleNextStepAndSendCode(): void {
    this.sendVerificationCode(true);
  }

  sendVerificationCode(moveToNextStep: boolean = false) {
    this.isLoadingSendOtp = true;
    const input = this.registrationForm.get('email_phone')?.value?.trim();

    let params: { email?: string; phone?: string } = {};
    if (DeveloperRegistrationStepperComponent.isEmail(input)) {
      params.email = input;
    } else {
      params.phone = input;
    }

    console.log(params);
    this.authenticationService.sendOtp(params).subscribe(
      (response: any) => {
        console.log('OTP sent:', response);
        this.isLoadingSendOtp = false;
        this.startCountdown();

        // Only move to next step if requested
        if (moveToNextStep) {
          this.nextStep();
        }

        this.cd.markForCheck();
      },
      (error: any) => {
        console.error('Failed to send OTP:', error);
        this.isLoadingSendOtp = false;
        this.cd.markForCheck();
      }
    );
  }

  checkOTP() {
    this.isLoadingCheckOtp = true;
    const input = this.registrationForm.get('email_phone')?.value?.trim();
    const codeArray = this.registrationForm.get('verificationCode')?.value;
    const otp = codeArray.join('');

    let params: { email?: string; phone?: string; otp?: number } = {};
    if (DeveloperRegistrationStepperComponent.isEmail(input)) {
      params.email = input;
    } else {
      params.phone = input;
    }
    params.otp = otp;

    console.log('Checking OTP with params:', params);

    this.authenticationService.checkOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP checked successfully:', response);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = '';
        this.cd.markForCheck();
        this.nextStep();
      },
      error: (error: any) => {
        console.error('Failed to check OTP:', error);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = error?.error?.message || 'Invalid verification code';
         this.clearOtpInputs();
        this.cd.markForCheck();
      },
    });
  }

  private clearOtpInputs() {
    this.verificationDigits = ['', '', '', '', ''];
    const verificationCodeArray = this.registrationForm.get(
      'verificationCode'
    ) as FormArray;
    verificationCodeArray.controls.forEach((control) => control.setValue(''));
  }

  createAccount(): void {
    this.isLoadingCreateAccount = true;
    const emailValue = this.registrationForm.get('email')?.value?.trim();

    let params = this.registrationForm.value;
    params.role = 'developer';

    // Handle optional email field - only include if it has a value
    if (emailValue && emailValue.length > 0) {
      params.email = emailValue;
    } else {
      // Remove email from params if it's empty
      delete params.email;
    }

    const formData = new FormData();

    // Append all form fields
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        formData.append(key, params[key]);
      }
    }

    // Append uploaded files (if any)
    for (const fileType in this.uploadedFiles) {
      const files = this.uploadedFiles[fileType];
      if (files?.length) {
         formData.append(fileType, files[0]);
      }
    }

    this.authenticationService.register(formData).subscribe({
      next: (response: any) => {
        this.isLoadingCreateAccount = false;
        let user = response.data;
        localStorage.setItem('authToken', user.authToken);
        this.authenticationService.setCurrentUser(response.data);
        this.cd.markForCheck();
        this.nextStep();
      },
      error: (error: any) => {
        this.isLoadingCreateAccount = false;

         let errorMessage = 'Failed to create account. Please try again.';
        if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        Swal.fire(errorMessage);
        this.cd.markForCheck();
      },
    });
  }

  startCountdown() {
    this.showResendButton = false;
    this.countdown = 30;

    const intervalId = setInterval(() => {
      this.countdown--;
      if (this.countdown === 0) {
        clearInterval(intervalId);
        this.showResendButton = true;
      }
      this.cd.markForCheck();
    }, 1000);
  }

  autoFocusNext(event: any, index: number): void {
    const input = event.target;
    if (input.value && index < 5) {
      const nextInput =
        input.parentElement?.nextElementSibling?.querySelector('input');
      nextInput?.focus();
    }
  }

  onResendCode() {
    this.sendVerificationCode(false);
  }

  // Toggle password visibility
  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  // File management
  onFileChange(event: Event, fileType: string): void {
    const files = (event.target as HTMLInputElement).files;
    if (!files?.length) return;

    this.uploadedFiles[fileType] = [
      ...(this.uploadedFiles[fileType] || []),
      ...Array.from(files),
    ];
    console.log(`Uploaded ${files.length} file(s) for ${fileType}`);
  }

  getFileCount(fileType: string): number {
    return this.uploadedFiles[fileType]?.length || 0;
  }
}
