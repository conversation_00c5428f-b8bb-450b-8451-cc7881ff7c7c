import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BrokerRegistrationData, ClientRegistrationData, DeveloperRegistrationData } from '../../models';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
})
export class RegisterComponent implements OnInit {
  // Step management
  currentStep = 1;
  selectedUserType: string | null = null;

  constructor(
    private router: Router,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();
    console.log('Current language:', currentLang);

    // Set the language in TranslateService
    this.translateService.use(currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
  }

  // User type selection methods
  selectUserType(userType: string): void {
    this.selectedUserType = userType;
  }

  // Step navigation methods
  nextStep(): void {
    if (this.currentStep === 1 && this.selectedUserType) {
      this.currentStep = 2;
    }
  }

  previousStep(): void {
    if (this.currentStep === 2) {
      this.currentStep = 1;
    }
  }

  // Handle registration completion from stepper
  onRegistrationComplete(registrationData: BrokerRegistrationData | ClientRegistrationData | DeveloperRegistrationData): void {
    console.log('Registration completed:', registrationData);
    // Navigate to login page after successful registration
    this.router.navigate(['/authentication/login']);
  }
}
