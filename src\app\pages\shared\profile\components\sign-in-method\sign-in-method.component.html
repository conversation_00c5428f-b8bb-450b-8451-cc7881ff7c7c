<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 bg-light-dark-blue" role="button" data-bs-toggle="collapse"
    data-bs-target="#kt_account_signin_method">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{ 'PROFILE.SIGN_IN_METHOD.TITLE' | translate }}</h3>
    </div>
  </div>

  <div id="kt_account_signin_method" class="collapse show">
    <div class="card-body border-top p-9">
      <ng-container *ngIf="!showChangeEmailForm">
        <div class="d-flex flex-wrap align-items-center">
          <div id="kt_signin_email" class="false">
            <div class="fs-6 fw-bolder mb-1">{{ 'PROFILE.SIGN_IN_METHOD.EMAIL_ADDRESS' | translate }}</div>
            <div class="fw-bold text-gray-600">
              {{ user.email }}
            </div>
          </div>

          <div id="kt_signin_email_edit" class="flex-row-fluid d-none">
            <form id="kt_signin_change_email" class="form" novalidate="">
              <div class="row mb-6">
                <div class="col-lg-6 mb-4 mb-lg-0">
                  <div class="fv-row mb-0">
                    <label for="emailaddress" class="form-label fs-6 fw-bolder mb-3">
                      {{ 'PROFILE.SIGN_IN_METHOD.ENTER_NEW_EMAIL' | translate }}
                    </label>
                    <input type="email" class="form-control form-control-lg form-control-solid" id="emailaddress"
                      [placeholder]="'PROFILE.SIGN_IN_METHOD.EMAIL_PLACEHOLDER' | translate" name="newEmail"
                      value="{{ user.email }}" />
                  </div>
                </div>

                <div class="col-lg-6">
                  <div class="fv-row mb-0">
                    <label for="confirmemailpassword" class="form-label fs-6 fw-bolder mb-3">
                      Confirm Password
                    </label>
                    <input type="password" class="form-control form-control-lg form-control-solid"
                      id="confirmemailpassword" name="confirmPassword" value="" />
                    <div class="fv-plugins-message-container">
                      <div class="fv-help-block">Password is required</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex">
                <button id="kt_signin_submit" type="submit" class="btn btn-dark-blue me-2 px-6">
                  Update Email
                </button>
                <button id="kt_signin_cancel" type="button"
                  class="btn btn-color-gray-500 btn-active-light-dark-blue px-6">
                  Cancel
                </button>
              </div>
            </form>
          </div>

          <div id="kt_signin_email_button" class="ms-auto false">
            <button class="btn btn-dark-blue btn-active-light-dark-blue px-9" (click)="toggleEmailForm(true)">
              Change Email
            </button>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="showChangeEmailForm">
        <div class="d-flex flex-wrap align-items-center">
          <div id="kt_signin_email" class="d-none">
            <div class="fs-6 fw-bolder mb-1">Email Address</div>
            <div class="fw-bold text-gray-600">
              {{ user.email }}
            </div>
          </div>

          <div id="kt_signin_email_edit" class="flex-row-fluid false">
            <form id="kt_signin_change_email" class="form" novalidate="">
              <div class="row mb-6">
                <div class="col-lg-6 mb-4 mb-lg-0">
                  <div class="fv-row mb-0">
                    <label for="emailaddress" class="form-label fs-6 fw-bolder mb-3">
                      Enter New Email Address
                    </label>
                    <input type="email" class="form-control form-control-lg form-control-solid" id="emailaddress"
                      placeholder="Email Address" name="newEmail" [(ngModel)]="newEmail" #newEmailRef="ngModel" required
                      pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" />
                  </div>
                  <div *ngIf="
                      newEmailRef.invalid &&
                      (newEmailRef.dirty || newEmailRef.touched)
                    " class="text-danger mt-2">
                    <div *ngIf="newEmailRef.errors?.['required']">
                      Email address is required.
                    </div>
                    <div *ngIf="newEmailRef.errors?.['pattern']">
                      <P>
                        Please enter a valid email format (e.g.
                        user&#64;example.com).
                      </P>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex">
                <button id="kt_signin_submit" type="button"
                  class="btn btn-light-dark-blue btn-active-dark-blue me-2 px-6"
                  [disabled]="newEmailRef.invalid || localLoading" (click)="saveEmail()">
                  <ng-container *ngIf="!localLoading">
                    Update Email
                  </ng-container>
                  <ng-container *ngIf="localLoading">
                    <span class="indicator-progress" [style.display]="'block'">
                      Please wait...{{ " " }}
                      <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                  </ng-container>
                </button>
                <button id="kt_signin_cancel" type="button"
                  class="btn btn-color-gray-500 btn-active-light-dark-blue px-6" (click)="toggleEmailForm(false)">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </ng-container>

      <div class="separator separator-dashed my-6"></div>

      <ng-container *ngIf="!showChangePasswordForm">
        <div class="d-flex flex-wrap align-items-center mb-10">
          <div id="kt_signin_password" class="false">
            <div class="fs-6 fw-bolder mb-1">Password</div>
            <div class="fw-bold text-gray-600">************</div>
          </div>
          <div id="kt_signin_password_button" class="ms-auto false">
            <button class="btn btn-dark-blue btn-active-light-dark-blue" (click)="togglePasswordForm(true)">
              Reset Password
            </button>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="showChangePasswordForm">
        <div class="d-flex flex-wrap align-items-center mb-10">
          <div id="kt_signin_password_edit" class="flex-row-fluid false">
            <form id="kt_signin_change_password" class="form" novalidate="">
              <div class="row mb-1">
                <div class="col-lg-4">
                  <div class="fv-row mb-0">
                    <label for="newpassword" class="form-label fs-6 fw-bolder mb-3">
                      New Password
                    </label>
                    <input type="password" class="form-control form-control-lg form-control-solid" id="newpassword"
                      name="newPassword" value="" [(ngModel)]="newPassword" />
                  </div>
                </div>
                <div class="col-lg-4">
                  <div class="fv-row mb-0">
                    <label for="confirmpassword" class="form-label fs-6 fw-bolder mb-3">
                      Confirm New Password
                    </label>
                    <input type="password" class="form-control form-control-lg form-control-solid" id="confirmpassword"
                      name="passwordConfirmation" value="{{ user.password }}" [(ngModel)]="confirmPassword" />
                  </div>
                </div>
              </div>

              <div class="form-text mb-5">
                Password must be at least 8 character and contain symbols
              </div>

              <div class="d-flex">
                <button id="kt_password_submit" type="button"
                  class="btn btn-light-dark-blue btn-active-dark-blue me-2 px-6" (click)="savePassword()">
                  <ng-container *ngIf="!localLoading">
                    Update Password
                  </ng-container>
                  <ng-container *ngIf="localLoading">
                    <span class="indicator-progress" [style.display]="'block'">
                      Please wait...{{ " " }}
                      <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                  </ng-container>
                </button>

                <button id="kt_password_cancel" type="button"
                  class="btn btn-color-gray-500 btn-active-light-dark-blue px-6" (click)="togglePasswordForm(false)">
                  Cancel
                </button>
              </div>
            </form>
          </div>

          <div id="kt_signin_password_button" class="ms-auto d-none">
            <button class="btn btn-light btn-active-light-primary">
              Reset Password
            </button>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>