// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .form-control {
    text-align: right;
  }

  .d-flex {
    flex-direction: row-reverse;

    &.align-items-center {
      flex-direction: row-reverse;
    }
  }

  .me-2, .me-3 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  .btn {
    margin-left: 0.5rem;
    margin-right: 0;
  }

  .form-text {
    text-align: right;
  }

  .fv-help-block {
    text-align: right;
  }
}
