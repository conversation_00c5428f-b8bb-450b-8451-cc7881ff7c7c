// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .card-header {
    text-align: right;

    .card-title {
      width: 100%;
      text-align: right;
      justify-content: flex-end;
      display: flex;

      h3 {
        text-align: right;
        margin-left: auto;
        margin-right: 0;
      }
    }
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .form-control {
    text-align: right;
  }

  // إصلاح محاذاة العناصر الرئيسية
  .d-flex.flex-wrap.align-items-center {
    flex-direction: row-reverse !important;
    justify-content: space-between !important;
    align-items: center !important;

    // قسم المعلومات (الإيميل/كلمة المرور)
    #kt_signin_email,
    #kt_signin_password {
      order: 2 !important;
      text-align: right !important;
      flex: 1 !important;

      .fs-6.fw-bolder {
        text-align: right !important;
      }

      .fw-bold.text-gray-600 {
        text-align: right !important;
      }
    }

    // زر التغيير
    #kt_signin_email_button,
    #kt_signin_password_button {
      order: 1 !important;
      margin-left: 0 !important;
      margin-right: auto !important;

      .btn {
        margin: 0 !important;
      }
    }
  }

  // إصلاح النماذج
  .flex-row-fluid {
    width: 100% !important;

    .form {
      width: 100% !important;
    }

    .row {
      direction: rtl !important;
    }

    .d-flex {
      justify-content: flex-start !important;

      .btn {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;

        &:last-child {
          margin-right: 0 !important;
        }
      }
    }
  }

  .form-text {
    text-align: right;
  }

  .fv-help-block {
    text-align: right;
  }

  .text-danger {
    text-align: right !important;
  }

  .separator {
    margin: 1.5rem 0 !important;
  }
}
