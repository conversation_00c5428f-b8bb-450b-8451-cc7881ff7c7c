import {
  Component,
  Output,
  EventEmitter,
  OnInit,
  ChangeDetectorRef,
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ClientRegistrationData } from '../../models';
import { AuthenticationService } from '../../services/authentication.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../../modules/i18n/translation.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-client-registration-stepper',
  templateUrl: './client-registration-stepper.component.html',
  styleUrls: ['./client-registration-stepper.component.scss'],
})
export class ClientRegistrationStepperComponent implements OnInit {
  @Output() onBack = new EventEmitter<void>();
  @Output() onComplete = new EventEmitter<ClientRegistrationData>();

  registrationForm: FormGroup;
  currentStep = 1;
  totalSteps = 4;
  selectedGender = '';
  verificationDigits: string[] = ['', '', '', '', ''];
  countdown: number = 25;
  showResendButton: boolean = false;
  isLoadingSendOtp: boolean = false;
  isLoadingCheckOtp: boolean = false;
  isLoadingCreateAccount: boolean = false;
  otpErrorMessage: string = '';

  // Validators
  static noNumbers = Validators.pattern(/^[^0-9]*$/);
  static emailOrPhonePattern = Validators.pattern(/^([^\s@]+@[^\s@]+\.[^\s@]+|01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);

  private static isEmail(value: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
  }

  constructor(
    private fb: FormBuilder,
    private authenticationService: AuthenticationService,
    private cd: ChangeDetectorRef,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    this.registrationForm = this.createForm();
  }

  ngOnInit(): void {
    this.initializeTranslations();
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();

    // Set the language in TranslateService
    this.translateService.use(currentLang);

    // Apply direction
    this.translationService.setLanguage(currentLang);
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Step 1: Gender, Full Name, Email/Phone
      gender: ['', [Validators.required]],
      fullName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          ClientRegistrationStepperComponent.noNumbers,
        ],
      ],
      email_phone: [
        '',
        [Validators.required, ClientRegistrationStepperComponent.emailOrPhonePattern],
      ],

      // Step 2: Verification Code
      verificationCode: this.fb.array(
        Array(5)
          .fill('')
          .map(() =>
            this.fb.control('', [
              Validators.required,
              Validators.pattern('[0-9]'),
            ])
          )
      ),

      // Step 3: Email, Phone, Password, Terms
      email: ['', [Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/)]],
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
        ],
      ],
      password_confirmation: ['', [Validators.required]],
      agreeTerms: [false, [Validators.requiredTrue]],
    });
  }

  get verificationCodeControls() {
    return (this.registrationForm.get('verificationCode') as FormArray)
      .controls;
  }

  // Helper methods for validation
  isFieldInvalid(fieldName: string): boolean {
    const field = this.registrationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  markFieldAsTouched(fieldName: string): void {
    this.registrationForm.get(fieldName)?.markAsTouched();
  }

  getFieldError(fieldName: string): string {
    const field = this.registrationForm.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;
    if (errors['required']) return 'This field is required';
    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';
    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';
    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';
    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';
    if (errors['email']) return 'Enter valid email';
    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;

    return 'Invalid input';
  }

  getFormError(): string {
    const password = this.registrationForm.get('password')?.value;
    const confirmPassword = this.registrationForm.get(
      'password_confirmation'
    )?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      return 'Passwords do not match';
    }
    return '';
  }

  // Check if step is valid
  isStep1Valid(): boolean {
    const gender = this.registrationForm.get('gender');
    const fullName = this.registrationForm.get('fullName');
    const emailPhone = this.registrationForm.get('email_phone');

    return !!(
      gender?.valid &&
      fullName?.valid &&
      emailPhone?.valid &&
      this.selectedGender
    );
  }

  isStep2Valid(): boolean {
    const verificationCode = this.registrationForm.get(
      'verificationCode'
    ) as FormArray;
    return verificationCode.valid;
  }

  isStep3Valid(): boolean {
    const email = this.registrationForm.get('email');
    const phone = this.registrationForm.get('phone');
    const password = this.registrationForm.get('password');
    const passwordConfirmation = this.registrationForm.get(
      'password_confirmation'
    );
    const agreeTerms = this.registrationForm.get('agreeTerms');

    // Check if passwords match
    const passwordsMatch = password?.value === passwordConfirmation?.value;

    return !!(
      email?.valid &&
      phone?.valid &&
      password?.valid &&
      passwordConfirmation?.valid &&
      agreeTerms?.valid &&
      passwordsMatch
    );
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.onBack.emit();
    }
  }

  selectGender(gender: string): void {
    this.selectedGender = gender;
    this.registrationForm.patchValue({ gender });
    this.registrationForm.get('gender')?.markAsTouched();
  }

  onDigitInput(index: number): void {
    const code = this.verificationDigits.join('');
    this.registrationForm.patchValue({ verificationCode: code });
  }

  handleNextStepAndSendCode(): void {
    this.sendVerificationCode(true); // true = move to next step
  }

  sendVerificationCode(moveToNextStep: boolean = false) {
    this.isLoadingSendOtp = true;
    const input = this.registrationForm.get('email_phone')?.value?.trim();

    let params: { email?: string; phone?: string } = {};
    if (ClientRegistrationStepperComponent.isEmail(input)) {
      params.email = input;
    } else {
      params.phone = input;
    }

    console.log(params);
    this.authenticationService.sendOtp(params).subscribe(
      (response: any) => {
        console.log('OTP sent:', response);
        this.isLoadingSendOtp = false;
        this.startCountdown();

        // Only move to next step if requested
        if (moveToNextStep) {
          this.nextStep();
        }

        this.cd.markForCheck();
      },
      (error: any) => {
        console.error('Failed to send OTP:', error);
        this.isLoadingSendOtp = false;
        this.cd.markForCheck();
      }
    );
  }

  checkOTP() {
    this.isLoadingCheckOtp = true;
    const input = this.registrationForm.get('email_phone')?.value?.trim();
    const codeArray = this.registrationForm.get('verificationCode')?.value;
    const otp = codeArray.join('');

    let params: { email?: string; phone?: string; otp?: number } = {};
    if (ClientRegistrationStepperComponent.isEmail(input)) {
      params.email = input;
    } else {
      params.phone = input;
    }

    params.otp = otp;
    console.log('Checking OTP with params:', params);

    this.authenticationService.checkOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP checked successfully:', response);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = '';
        this.cd.markForCheck();
        this.nextStep();
      },
      error: (error: any) => {
        console.error('Failed to check OTP:', error);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = error?.error?.message;
        // Clear the OTP inputs
        this.clearOtpInputs();

        this.cd.markForCheck();
      },
    });
  }

  private clearOtpInputs() {
    this.verificationDigits = ['', '', '', '', ''];
    const verificationCodeArray = this.registrationForm.get(
      'verificationCode'
    ) as FormArray;
    verificationCodeArray.controls.forEach((control) => control.setValue(''));
  }

  createAccount(): void {
    this.isLoadingCreateAccount = true;

    const input = this.registrationForm.get('email_phone')?.value?.trim();
    const emailValue = this.registrationForm.get('email')?.value?.trim();

    let params: any = { ...this.registrationForm.value };

    // Handle email_phone field
    if (ClientRegistrationStepperComponent.isEmail(input)) {
      params.email = input;
    } else {
      params.phone = input;
    }

    // Handle optional email field - only include if it has a value
    if (emailValue && emailValue.length > 0) {
      params.email = emailValue;
    } else {
      // Remove email from params if it's empty
      delete params.email;
    }

    params.role = 'client';

    this.authenticationService.register(params).subscribe({
      next: (response: any) => {
        console.log('registered successfully', response);
        this.isLoadingCreateAccount = false;
        let user = response.data;
        localStorage.setItem('authToken', user.authToken);
        this.authenticationService.setCurrentUser(response.data);

        this.cd.markForCheck();
        this.nextStep();
      },
      error: (error: any) => {
        console.error('Failed to register:', error);
        this.isLoadingCreateAccount = false;

        // Better error handling
        let errorMessage = 'Failed to create account. Please try again.';
        if (error?.error?.message) {
          errorMessage = error.error.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        Swal.fire(errorMessage);
        this.cd.markForCheck();
      },
    });
  }

  autoFocusNext(event: any, index: number): void {
    const input = event.target;
    if (input.value && index < 5) {
      const nextInput =
        input.parentElement?.nextElementSibling?.querySelector('input');
      nextInput?.focus();
    }
  }

  startCountdown() {
    this.showResendButton = false;
    this.countdown = 25;

    const intervalId = setInterval(() => {
      this.countdown--;
      if (this.countdown === 0) {
        clearInterval(intervalId);
        this.showResendButton = true;
      }
      this.cd.markForCheck();
    }, 1000);
  }
  onResendCode() {
    this.sendVerificationCode(false); // false = don't move to next step
  }
}
