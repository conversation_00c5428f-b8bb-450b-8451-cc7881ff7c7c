import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { AuthenticationRoutingModule } from './authentication-routing.module';
import { AuthenticationLayoutComponent } from './authentication-layout.component';
import { LoginComponent } from './components/login/login.component';
import { RegisterComponent } from './components/register/register.component';

import { ClientRegistrationStepperComponent } from './components/client-registration-stepper/client-registration-stepper.component';
import { BrokerRegistrationStepperComponent } from './components/broker-registration-stepper/broker-registration-stepper.component';
import { DeveloperRegistrationStepperComponent } from './components/developer-registration-stepper/developer-registration-stepper.component';

@NgModule({
  declarations: [
    AuthenticationLayoutComponent,
    LoginComponent,
    RegisterComponent,
    ClientRegistrationStepperComponent,
    BrokerRegistrationStepperComponent,
    DeveloperRegistrationStepperComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    TranslateModule,
    AuthenticationRoutingModule,
  ],
})
export class AuthenticationModule {}
