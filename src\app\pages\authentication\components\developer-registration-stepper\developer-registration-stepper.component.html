<div class="client-registration-stepper">
  <!-- Stepper Header -->
  <div class="stepper-header">
    <h2 class="stepper-title">{{ 'AUTH.DEVELOPER_REGISTRATION.TITLE' | translate }}</h2>
    <div class="stepper-progress">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
      </div>
      <div class="d-flex">
        <span class="progress-text">{{ 'AUTH.CLIENT_REGISTRATION.STEP_OF' | translate: {current: currentStep, total:
          totalSteps} }}</span>
        <button *ngIf="currentStep > 0 && currentStep < 5" type="button" class="back-to-previous"
          (click)="previousStep()">
          {{ 'AUTH.CLIENT_REGISTRATION.BACK_TO_PREVIOUS' | translate }}
        </button>
      </div>
    </div>
  </div>

  <!-- Stepper Content -->
  <form [formGroup]="registrationForm" class="stepper-form">
    <!-- Step 1: Basic Information -->
    <div *ngIf="currentStep === 1" class="step-content">
      <h3 class="step-title">{{ 'AUTH.DEVELOPER_REGISTRATION.BASIC_INFO' | translate }}</h3>

      <div class="form-group">
        <label for="fullName" class="form-label">
          <i class="ki-outline ki-office-bag"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.COMPANY_NAME' | translate }} <span class="required"></span>
        </label>
        <input type="text" id="fullName" formControlName="fullName" class="form-control"
          [class.is-invalid]="isFieldInvalid('fullName')"
          [placeholder]="'AUTH.DEVELOPER_REGISTRATION.COMPANY_NAME_PLACEHOLDER' | translate" pattern="[^0-9]*"
          title="Company name cannot contain numbers" (blur)="markFieldAsTouched('fullName')" required />
        <div *ngIf="isFieldInvalid('fullName')" class="invalid-feedback">
          {{ getFieldError("fullName") }}
        </div>
      </div>



      <!-- Email or Phone -->
      <div class="form-group">
        <label class="form-label">
          <i class="ki-outline ki-phone"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.COMPANY_EMAIL_PHONE' | translate }} <span class="required"></span>
        </label>
        <input type="text" formControlName="email_phone" class="form-control"
          [class.is-invalid]="isFieldInvalid('email_phone')"
          [placeholder]="'AUTH.INPUT.EMAIL_OR_PHONE_PLACEHOLDER' | translate"
          title="Enter a valid email address or phone number" autocomplete="email tel"
          (blur)="markFieldAsTouched('email_phone')" required />
        <div *ngIf="isFieldInvalid('email_phone')" class="invalid-feedback">
          {{ getFieldError("email_phone") }}
        </div>
      </div>

      <!-- Send Verification Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingSendOtp"
        [disabled]="!isStep1Valid() || isLoadingSendOtp" (click)="handleNextStepAndSendCode()">
        <span *ngIf="isLoadingSendOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingSendOtp">{{ 'AUTH.CLIENT_REGISTRATION.SENDING' | translate }}</span>
        <span *ngIf="!isLoadingSendOtp">{{ 'AUTH.CLIENT_REGISTRATION.SEND_VERIFICATION_CODE' | translate }}</span>
      </button>

      <!-- Help Text -->
      <div class="help-text">
        {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 2: Verification Code -->
    <div *ngIf="currentStep === 2" class="step-content">
      <h3 class="step-title">{{ 'AUTH.CLIENT_REGISTRATION.ENTER_VERIFICATION_CODE' | translate }}</h3>

      <!-- Verification Code Input -->
      <div class="verification-code-section">
        <div formArrayName="verificationCode" class="verification-inputs">
          <div class="code-input" *ngFor="let ctrl of verificationCodeControls; let i = index">
            <input type="text" maxlength="1" class="verification-input" [formControlName]="i"
              (input)="autoFocusNext($event, i)" />
          </div>
        </div>
      </div>

      <!-- Countdown Timer -->
      <div class="countdown-section">
        <span class="countdown-text" *ngIf="!showResendButton">
          {{ 'AUTH.VERIFICATION.RESEND_IN' | translate }}
          <span class="countdown-timer">
            0:{{ countdown < 10 ? "0" + countdown : countdown }} </span>
          </span>

          <button *ngIf="showResendButton" class="btn btn-link" (click)="onResendCode()">
            {{ 'AUTH.VERIFICATION.RESEND_CODE' | translate }}
          </button>
      </div>

      <!-- OTP Error Message -->
      <div *ngIf="otpErrorMessage" class="alert alert-danger mt-3" role="alert">
        {{ otpErrorMessage }}
      </div>

      <!-- Next Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCheckOtp"
        [disabled]="!isStep2Valid() || isLoadingCheckOtp" (click)="checkOTP()">
        <span *ngIf="isLoadingCheckOtp" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCheckOtp">{{ 'AUTH.CLIENT_REGISTRATION.VERIFYING' | translate }}</span>
        <span *ngIf="!isLoadingCheckOtp">{{ 'AUTH.CLIENT_REGISTRATION.VERIFICATION_CODE_NEXT' | translate }}</span>
      </button>

      <!-- Help Text -->
      <div class="help-text">
        {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 3: Documents Upload -->
    <div *ngIf="currentStep === 3" class="step-content">
      <h3 class="step-title">{{ 'AUTH.DEVELOPER_REGISTRATION.UPLOAD_DOCUMENTS' | translate }}</h3>

      <div class="documents-section">
        <p class="documents-description">
          {{ 'AUTH.DEVELOPER_REGISTRATION.DOCUMENTS_DESC' | translate }}
        </p>

        <!-- Document Upload Cards -->
        <div class="upload-card-container">
          <!-- Company Registration Certificate -->
          <div class="card mb-3 cursor-pointer">
            <label for="image" class="card-body text-center py-2">
              <div class="upload-icon cursor-pointer">
                <i class="fas fa-arrow-up"></i>
              </div>
              <span class="upload-text cursor-pointer">
                {{ 'AUTH.DEVELOPER_REGISTRATION.COMPANY_LOGO' | translate }}
                <span *ngIf="getFileCount('image') > 0" class="badge bg-success ms-2">
                  {{ getFileCount("image") }}
                </span>
              </span>
              <div class="upload-subtitle">PNG, JPG</div>
              <input type="file" id="image" class="d-none" (change)="onFileChange($event, 'image')"
                accept=".png,.jpg,.jpeg" />
            </label>
          </div>

          <!-- Commercial Registration -->
          <div class="card mb-3 cursor-pointer">
            <label for="commercialRegistryImage" class="card-body text-center py-2">
              <div class="upload-icon cursor-pointer">
                <i class="fas fa-arrow-up"></i>
              </div>
              <span class="upload-text cursor-pointer">
                {{ 'AUTH.DEVELOPER_REGISTRATION.COMMERCIAL_REGISTER' | translate }}
                <span *ngIf="getFileCount('commercialRegistryImage') > 0" class="badge bg-success ms-2">
                  {{ getFileCount("commercialRegistryImage") }}
                </span>
              </span>
              <div class="upload-subtitle">PNG, JPG, PDF</div>
              <input type="file" id="commercialRegistryImage" class="d-none"
                (change)="onFileChange($event, 'commercialRegistryImage')" accept=".png,.jpg,.jpeg,.pdf" multiple />
            </label>
          </div>

          <!-- Tax Card -->
          <div class="card mb-3 cursor-pointer">
            <label for="taxCardImage" class="card-body text-center py-2">
              <div class="upload-icon cursor-pointer">
                <i class="fas fa-arrow-up"></i>
              </div>
              <span class="upload-text cursor-pointer">
                {{ 'AUTH.DEVELOPER_REGISTRATION.TAX_CARD' | translate }}
                <span *ngIf="getFileCount('taxCardImage') > 0" class="badge bg-success ms-2">
                  {{ getFileCount("taxCardImage") }}
                </span>
              </span>
              <div class="upload-subtitle">PNG, JPG, PDF</div>
              <input type="file" id="taxCardImage" class="d-none" (change)="onFileChange($event, 'taxCardImage')"
                accept=".png,.jpg,.jpeg,.pdf" multiple />
            </label>
          </div>
        </div>

        <!-- Upload Documents Button -->
        <button type="button" class="btn btn-primary btn-verification" (click)="nextStep()">
          {{ 'AUTH.DEVELOPER_REGISTRATION.NEXT' | translate }}
        </button>

        <!-- Skip Button -->
        <button type="button" class="btn btn-link skip-button" (click)="nextStep()">
          {{ 'AUTH.DEVELOPER_REGISTRATION.SKIP_FOR_NOW' | translate }}
          <i class="ki-outline ki-arrow-right"></i>
        </button>

        <!-- Help Text -->
        <div class="help-text">
          {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
            }}</span>
        </div>
      </div>
    </div>

    <!-- Step 4: Choose Your Areas -->
    <!-- <div *ngIf="currentStep === 4" class="step-content">
      <h3 class="step-title">{{ getStepTitle() }}</h3>

      <div class="areas-section">
        <p class="areas-description">
          You can now choose your work areas and specialization to reach clients or properties you want to advertise for
        </p>

        <!-- Work Scope Selection -->
    <!-- <div class="work-scope-section">
          <h4 class="work-scope-title">Your work scope</h4>
          <div class="work-scope-options">
            <div class="scope-option">
              <input type="radio" id="outside-compound" name="workScope" value="outside" class="scope-radio">
              <label for="outside-compound" class="scope-label">Outside Compound</label>
            </div>
            <div class="scope-option">
              <input type="radio" id="inside-compound" name="workScope" value="inside" class="scope-radio">
              <label for="inside-compound" class="scope-label">Inside Compound</label>
            </div>
            <div class="scope-option">
              <input type="radio" id="both-scope" name="workScope" value="both" class="scope-radio">
              <label for="both-scope" class="scope-label">Both</label>
            </div>
          </div>
        </div> -->

    <!-- Area Selection Items -->
    <!-- <div class="area-item">
          <div class="area-header">
            <span class="area-title">Enter your work scope</span>
          </div>
          <div class="area-dropdown">
            <select class="form-control">
              <option value="" disabled selected>Choose main governorates/areas</option>
              <option value="cairo">Cairo</option>
              <option value="giza">Giza</option>
              <option value="alexandria">Alexandria</option>
            </select>
          </div>
        </div>

        <div class="area-item">
          <div class="area-header">
            <span class="area-title">Enter sub-areas </span>
          </div>
          <div class="area-dropdown">
            <select class="form-control">
              <option value="" disabled selected>Choose specific sub-areas </option>
              <option value="maadi">Maadi</option>
              <option value="zamalek">Zamalek</option>
              <option value="heliopolis">Heliopolis</option>
            </select>
          </div>
        </div>

        <div class="area-item">
          <div class="area-header">
            <span class="area-title">Enter specializations you develop</span>
          </div>
          <div class="area-dropdown">
            <select class="form-control">
              <option value="" disabled selected>Choose specialization - types of properties you develop</option>
              <option value="residential">Residential</option>
              <option value="commercial">Commercial</option>
              <option value="administrative">Administrative</option>
            </select>
          </div>
        </div> -->

    <!-- Complete Registration Button -->
    <!-- <button type="button" class="btn btn-primary btn-verification" (click)="nextStep()">
          Complete Registration
        </button> -->

    <!-- Skip Button -->
    <!-- <button type="button" class="btn btn-link skip-button" (click)="nextStep()">
          Skip and return later
          <i class="ki-outline ki-arrow-right"></i>
        </button> -->

    <!-- Help Text -->
    <!-- <div class="help-text">
          Need help? <span class="contact-link">Contact us</span>
        </div>
      </div>
    </div> -->

    <!-- Step 4: Phone, Email and Password -->
    <div *ngIf="currentStep === 4" class="step-content">
      <h3 class="step-title">{{ 'AUTH.DEVELOPER_REGISTRATION.ACCOUNT_DETAILS' | translate }}</h3>

      <!-- Phone -->
      <div class="form-group">
        <label for="phone" class="form-label">
          <i class="ki-outline ki-phone"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.PHONE' | translate }} <span class="required"></span>
        </label>
        <input type="tel" id="phone" formControlName="phone" class="form-control"
          [class.is-invalid]="isFieldInvalid('phone')" [placeholder]="'AUTH.INPUT.PHONE_PLACEHOLDER' | translate"
          required autocomplete="tel" />
        <div *ngIf="isFieldInvalid('phone')" class="invalid-feedback">
          {{ getFieldError("phone") }}
        </div>
      </div>

      <!-- Email -->
      <div class="form-group">
        <label for="email" class="form-label">
          <i class="ki-outline ki-user"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.EMAIL' | translate }}
        </label>
        <input type="email" id="email" formControlName="email" class="form-control"
          [class.is-invalid]="isFieldInvalid('email')" [placeholder]="'AUTH.INPUT.EMAIL_PLACEHOLDER' | translate"
          autocomplete="email" />
        <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
          {{ getFieldError("email") }}
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password" class="form-label">
          <i class="ki-outline ki-lock"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.PASSWORD' | translate }} <span class="required"></span>
        </label>
        <div class="password-input-container">
          <input [type]="showPassword ? 'text' : 'password'" id="password" formControlName="password"
            class="form-control" [class.is-invalid]="isFieldInvalid('password')"
            [placeholder]="'AUTH.INPUT.PASSWORD_PLACEHOLDER' | translate" minlength="8"
            pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$"
            title="Password must be at least 8 characters with uppercase, lowercase and number" required
            autocomplete="new-password" />
          <i [class]="showPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'" class="password-toggle-icon"
            (click)="togglePasswordVisibility()"></i>
        </div>
        <div *ngIf="isFieldInvalid('password')" class="invalid-feedback">
          {{ getFieldError("password") }}
        </div>
      </div>

      <!--Confirm Password -->
      <div class="form-group">
        <label for="confirmPassword" class="form-label">
          <i class="ki-outline ki-lock"></i>
          {{ 'AUTH.DEVELOPER_REGISTRATION.CONFIRM_PASSWORD' | translate }} <span class="required"></span>
        </label>
        <div class="password-input-container">
          <input [type]="showConfirmPassword ? 'text' : 'password'" id="confirmPassword"
            formControlName="password_confirmation" class="form-control" [class.is-invalid]="
              isFieldInvalid('password_confirmation') || getFormError()
            " [placeholder]="'AUTH.INPUT.PASSWORD_PLACEHOLDER' | translate" required autocomplete="new-password" />
          <i [class]="showConfirmPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'"
            class="password-toggle-icon" (click)="toggleConfirmPasswordVisibility()"></i>
        </div>
        <div *ngIf="isFieldInvalid('password_confirmation')" class="invalid-feedback">
          {{ getFieldError("password_confirmation") }}
        </div>
        <div *ngIf="getFormError()" class="invalid-feedback">
          {{ getFormError() }}
        </div>
      </div>

      <!-- Terms Agreement -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" id="agreeTerms" formControlName="agreeTerms" class="form-check-input"
            [class.is-invalid]="isFieldInvalid('agreeTerms')" />
          <label for="agreeTerms" class="form-check-label">
            {{ 'AUTH.DEVELOPER_REGISTRATION.AGREE_TERMS' | translate }} <span class="required"></span>
          </label>
        </div>
        <div *ngIf="isFieldInvalid('agreeTerms')" class="invalid-feedback">
          {{ getFieldError("agreeTerms") }}
        </div>
      </div>

      <!-- Create Account Button -->
      <button type="button" class="btn btn-primary btn-verification" [class.loading]="isLoadingCreateAccount"
        [disabled]="!isStep4Valid() || isLoadingCreateAccount" (click)="createAccount()">
        <span *ngIf="isLoadingCreateAccount" class="spinner-border spinner-border-sm me-2" role="status"></span>
        <span *ngIf="isLoadingCreateAccount">{{ 'AUTH.DEVELOPER_REGISTRATION.CREATING_ACCOUNT' | translate }}</span>
        <span *ngIf="!isLoadingCreateAccount">{{ 'AUTH.DEVELOPER_REGISTRATION.CREATE_ACCOUNT' | translate }}</span>
      </button>

      <!-- Help Text -->
      <div class="help-text">
        {{ 'AUTH.GENERAL.NEED_HELP' | translate }} <span class="contact-link">{{ 'AUTH.GENERAL.CONTACT_US' | translate
          }}</span>
      </div>
    </div>

    <!-- Step 5: Success Page (Previously Step 6) -->
    <div *ngIf="currentStep === 5" class="step-content success-step">
      <div class="success-content">
        <div class="success-icon">
          <i class="ki-outline ki-check-circle"></i>
        </div>

        <h3 class="success-title">{{ 'AUTH.CLIENT_REGISTRATION.REGISTRATION_SUCCESS' | translate }}</h3>

        <p class="success-message">
          {{ 'AUTH.CLIENT_REGISTRATION.SUCCESS_MESSAGE' | translate }}
        </p>

        <div class="success-illustration">
          <!-- <img src="/assets/media/login/successfully.png" alt="Success" class="success-image" /> -->
          <!-- <img src="~src/assets/media/login/successfully.png" alt="Success" class="success-image" /> -->
          <img src="assets/media/login/successfully.png" alt="Success" class="success-image" />
        </div>

        <button type="button" class="btn btn-primary btn-success-action" [routerLink]="['/developer/dashboards']">
          {{ 'AUTH.CLIENT_REGISTRATION.GO_TO_WEBSITE' | translate }}
        </button>

        <div class="additional-info">
          <span class="info-link">{{ 'AUTH.CLIENT_REGISTRATION.LEARN_MORE' | translate }}</span>
        </div>
      </div>
    </div>
  </form>
</div>