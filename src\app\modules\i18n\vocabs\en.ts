// USA
export const locale = {
  lang: 'en',
  data: {
    TRANSLATOR: {
      SELECT: 'Select your language',
    },
    MENU: {
      NEW: 'new',
      ACTIONS: 'Actions',
      CREATE_POST: 'Create New Post',
      PAGES: 'Pages',
      FEATURES: 'Features',
      APPS: 'Apps',
      DASHBOARD: 'Dashboard',
      REQUESTS: 'Requests',
      DATA_PROPERTIES: 'Data & Properties',
      DEVELOPERS: 'Developers',
      SUBSCRIPTION: 'Subscription',
      PROJECTS: 'Projects',
      BROKERS: 'Brokers',
      USERS: 'Users',
      MESSAGES: 'Messages',
      PROFILE: 'Profile',
      NOTIFICATIONS: 'Notifications',
      HELP: 'Help',
      ADVERTISEMENTS: 'Advertisements',
      MAPS: 'Maps',
      SETTINGS: 'Settings'
    },
    AUTH: {
      GENERAL: {
        OR: 'Or',
        SUBMIT_BUTTON: 'Submit',
        NO_ACCOUNT: 'Don\'t have an account?',
        SIGNUP_BUTTON: 'Sign Up',
        FORGOT_BUTTON: 'Forgot Password',
        BACK_BUTTON: 'Back',
        PRIVACY: 'Privacy',
        LEGAL: 'Legal',
        CONTACT: 'Contact',
        NEED_HELP: 'Need help?',
        CONTACT_US: 'Contact us',
        ALREADY_HAVE_ACCOUNT: 'Already have an account?',
        GO_TO_LOGIN: 'Go to login',
        CREATE_ONE: 'Create one',
        DONT_HAVE_ACCOUNT: 'Don\'t have an account?'
      },
      LOGIN: {
        TITLE: 'Login to your account',
        BUTTON: 'Log In',
        PHONE: 'Phone',
        PASSWORD: 'Password',
        PHONE_PLACEHOLDER: '01xxxxxxxxx',
        PASSWORD_PLACEHOLDER: 'Enter your password',
        FORGOT_PASSWORD: 'Forgot your password?',
        REMEMBER_ME: 'Remember me',
        LOGGING_IN: 'Logging in...',
        LOG_IN: 'Log In'
      },
      FORGOT: {
        TITLE: 'Forgot Password',
        DESC: 'Don\'t worry, we\'ll send you password recovery instructions',
        SUCCESS: 'Your account has been successfully reset.',
        EMAIL_OR_MOBILE: 'Email or Mobile Number',
        EMAIL_OR_MOBILE_PLACEHOLDER: 'Enter your email or mobile number',
        SEND_VERIFICATION_CODE: 'Send Verification Code',
        SENDING: 'Sending...',
        BACK_TO_LOGIN: 'Back to Login'
      },
      VERIFICATION: {
        TITLE: 'Enter Verification Code',
        RESEND_IN: 'Resend in',
        RESEND_CODE: 'Resend Code',
        VERIFYING: 'Verifying...',
        VERIFIED_NEXT: 'Verified - Next',
        BACK_TO_FORGOT_PASSWORD: 'Back to Forgot Password'
      },
      RESET_PASSWORD: {
        TITLE: 'Reset Password',
        DESC: 'Enter your new password for your account',
        NEW_PASSWORD: 'New Password',
        NEW_PASSWORD_PLACEHOLDER: 'Enter your new password',
        CONFIRM_PASSWORD: 'Confirm Password',
        CONFIRM_PASSWORD_PLACEHOLDER: 'Re-enter your password',
        SAVE_NEW_PASSWORD: 'Save New Password',
        SAVING: 'Saving...',
        BACK_TO_VERIFICATION: 'Back to Verification Code'
      },
      REGISTER: {
        TITLE: 'Sign Up',
        DESC: 'Enter your details to create your account',
        SUCCESS: 'Your account has been successfuly registered.',
        CHOOSE_ACCOUNT_TYPE: 'Choose your account type',
        CHOOSE_ACCOUNT_DESC: 'Choose the account type that you want to register with, which matches your work and needs in Easy deal',
        CLIENT: 'Client',
        BROKER: 'Broker',
        DEVELOPER: 'Developer',
        CHOOSE: 'Choose'
      },
      INPUT: {
        EMAIL: 'Email',
        FULLNAME: 'Full Name',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: 'Username',
        PHONE: 'Phone',
        EMAIL_OR_PHONE: 'Enter your email or phone number',
        EMAIL_OR_PHONE_PLACEHOLDER: '<EMAIL> or 01xxxxxxxxx',
        EMAIL_PLACEHOLDER: '<EMAIL>..',
        PHONE_PLACEHOLDER: '01xxxxxxxxx',
        PASSWORD_PLACEHOLDER: '********',
        NAME_PLACEHOLDER: 'Name'
      },
      VALIDATION: {
        INVALID: '{{name}} is not valid',
        REQUIRED: '{{name}} is required',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        AGREEMENT_REQUIRED: 'Accepting terms & conditions are required',
        NOT_FOUND: 'The requested {{name}} is not found',
        INVALID_LOGIN: 'The login detail is incorrect',
        REQUIRED_FIELD: 'Required field',
        MIN_LENGTH_FIELD: 'Minimum field length:',
        MAX_LENGTH_FIELD: 'Maximum field length:',
        INVALID_FIELD: 'Field is not valid',
        SELECT_GENDER: 'Please select your gender',
        PASSWORDS_NOT_MATCH: 'Passwords do not match'
      },
      CLIENT_REGISTRATION: {
        TITLE: 'Register as a Client',
        STEP_OF: 'Step {{current}} of {{total}}',
        BACK_TO_PREVIOUS: 'Back to previous step',
        CHOOSE_GENDER: 'Choose Gender',
        FEMALE: 'Female',
        MALE: 'Male',
        FULL_NAME: 'Full Name',
        EMAIL_PHONE_PASSWORD: 'Email, Phone and Password',
        SEND_VERIFICATION_CODE: 'Send Verification Code',
        SENDING: 'Sending...',
        ENTER_VERIFICATION_CODE: 'Enter Verification Code',
        VERIFICATION_CODE_NEXT: 'Verification Code - Next',
        VERIFYING: 'Verifying...',
        CREATE_ACCOUNT: 'Create Account',
        CREATING_ACCOUNT: 'Creating Account...',
        REGISTRATION_SUCCESS: 'registration Success',
        SUCCESS_MESSAGE: 'Your account has been successfully created. You can now enjoy the various and amazing services provided by Easy deal through the website or dashboard.',
        GO_TO_WEBSITE: 'Go to website',
        LEARN_MORE: 'Learn all about your account and how to get started',
        AGREE_TERMS: 'I agree to the Terms and Conditions'
      },
      BROKER_REGISTRATION: {
        TITLE: 'Broker Registration',
        BASIC_INFO: 'Enter Your Basic Information',
        ENTER_FULL_NAME: 'Enter full name...',
        ENTER_EMAIL_PHONE: 'Enter Email or Phone Number',
        EMAIL_PHONE_EXAMPLE: '<EMAIL> or ***********',
        CHOOSE_BROKER_TYPE: 'Choose Broker Type',
        BROKER_TYPE_DESC: 'Please select the type of broker registration you want to proceed with',
        INDEPENDENT_BROKER: 'Independent Broker',
        INDEPENDENT_BROKER_DESC: 'Register as an individual real estate broker',
        REAL_ESTATE_COMPANY: 'Real Estate Company',
        REAL_ESTATE_COMPANY_DESC: 'Register as a real estate brokerage company',
        CONTINUE: 'Continue',
        UPLOAD_DOCUMENTS: 'Please Upload Required Documents',
        DOCUMENTS_DESC: 'You can upload the required documents now or skip and add them later when you first use the required services',
        PROFILE_PHOTO: 'Profile Photo',
        ID_DOCUMENT: 'ID Document',
        NATIONAL_ID_FRONT: 'National ID Front',
        NATIONAL_ID_BACK: 'National ID Back',
        COMPANY_LOGO: 'Company logo image for account',
        COMMERCIAL_REGISTER: 'Commercial register photo',
        TAX_CARD: 'Tax card image',
        SKIP_FOR_NOW: 'Skip for now',
        NEXT: 'Next'
      },
      DEVELOPER_REGISTRATION: {
        TITLE: 'Developer Registration',
        BASIC_INFO: 'Enter Your Basic Information',
        COMPANY_NAME: 'Company Name',
        COMPANY_NAME_PLACEHOLDER: 'Real Estate Development Company',
        COMPANY_EMAIL_PHONE: 'Company email or phone number',
        UPLOAD_DOCUMENTS: 'Please Upload Required Documents',
        DOCUMENTS_DESC: 'You can upload the required documents now or skip and add them later when you first use the required services',
        COMPANY_LOGO: 'Company logo image for account',
        COMMERCIAL_REGISTER: 'Commercial register photo',
        TAX_CARD: 'Tax card image',
        COMPANY_PROFILE: 'Company Profile',
        COMPANY_PROFILE_DESC: 'Company profile and business information',
        SKIP_FOR_NOW: 'Skip for now',
        NEXT: 'Next',
        CHOOSE_MAIN_AREAS: 'Choose main governorates/areas',
        CHOOSE_SUB_AREAS: 'Choose specific sub-areas',
        CAIRO: 'Cairo',
        GIZA: 'Giza',
        ALEXANDRIA: 'Alexandria',
        MAADI: 'Maadi',
        ZAMALEK: 'Zamalek',
        HELIOPOLIS: 'Heliopolis',
        ACCOUNT_DETAILS: 'Enter Your Account Details',
        PHONE: 'Phone',
        EMAIL: 'Email',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        AGREE_TERMS: 'I agree to the Terms and Conditions',
        CREATE_ACCOUNT: 'Create Account',
        CREATING_ACCOUNT: 'Creating Account...'
      }
    },
    PROFILE: {
      LOADING: 'Loading...',
      LOADING_PROFILE: 'Loading Profile...',
      CHANGE_PROFILE_PICTURE: 'Change Profile Picture',
      UPGRADE_PLAN: 'Upgrade Plan',
      SPECIALIZATIONS: 'Specializations',
      LOCATIONS: 'Locations',
      OPERATIONS: 'Operations',
      ADVERTISEMENTS: 'Advertisements',
      PROFILE_IMAGE_UPDATED_SUCCESS: 'Profile image updated successfully',
      PROFILE_IMAGE_UPDATE_FAILED: 'Failed to update profile image',
      PROFILE_UPDATED_SUCCESS: 'Profile updated successfully',
      PROFILE_UPDATE_FAILED: 'Failed to update profile',
      PROFILE_DETAILS: {
        TITLE: 'Profile Details',
        FULL_NAME: 'Full Name',
        FULL_NAME_PLACEHOLDER: 'Full name',
        FULL_NAME_REQUIRED: 'Full name is required.',
        PHONE_NUMBER: 'Phone Number',
        PHONE_NUMBER_PLACEHOLDER: 'Phone number',
        PHONE_NUMBER_REQUIRED: 'Phone number is required.',
        PHONE_NUMBER_PATTERN: 'Phone number must be a valid Egyptian number.',
        PHONE_NUMBER_LENGTH: 'Phone number must be 11 digits.',
        SAVE_CHANGES: 'Save Changes'
      },
      SIGN_IN_METHOD: {
        TITLE: 'Sign-in Method',
        EMAIL_ADDRESS: 'Email Address',
        ENTER_NEW_EMAIL: 'Enter New Email Address',
        EMAIL_PLACEHOLDER: 'Email Address',
        CONFIRM_PASSWORD: 'Confirm Password',
        PASSWORD_REQUIRED: 'Password is required',
        UPDATE_EMAIL: 'Update Email',
        CHANGE_EMAIL: 'Change Email',
        CANCEL: 'Cancel',
        PLEASE_WAIT: 'Please wait...',
        EMAIL_REQUIRED: 'Email address is required.',
        EMAIL_PATTERN: 'Please enter a valid email format (e.g. <EMAIL>).',
        PASSWORD: 'Password',
        RESET_PASSWORD: 'Reset Password',
        NEW_PASSWORD: 'New Password',
        CONFIRM_NEW_PASSWORD: 'Confirm New Password',
        PASSWORD_REQUIREMENTS: 'Password must be at least 8 character and contain symbols',
        UPDATE_PASSWORD: 'Update Password',
        EMAIL_CANNOT_BE_EMPTY: 'Email cannot be empty',
        EMAIL_UPDATED_SUCCESS: 'Email updated successfully',
        EMAIL_UPDATE_FAILED: 'Failed to update email',
        ENTER_ALL_PASSWORD_FIELDS: 'Please enter all password fields',
        PASSWORDS_DO_NOT_MATCH: 'Passwords do not match',
        PASSWORD_UPDATED_SUCCESS: 'Password updated successfully',
        PASSWORD_UPDATE_FAILED: 'Failed to update password'
      },
      ACCOUNT_DETAILS: {
        TITLE: 'Account Details',
        REGISTRATION_PAPERS: 'Registration papers',
        PAYMENTS_RENEWALS: 'Payments and Renewals'
      },
      ADVERTISEMENTS_DETAILS: {
        TITLE: 'Advertisements and Properties Details',
        SPECIALIZATIONS: 'Specializations',
        LOCATIONS: 'Locations',
        LOCATIONS_TOOLTIP: 'Areas can be changed after 17 days',
        ADD_NEW_LOCATION: 'Add New Location',
        CITY: 'City',
        LOADING_CITIES: 'Loading cities...',
        NO_CITIES: 'No cities available',
        SELECT_CITY: 'Select City',
        AREA: 'Area',
        SELECT_CITY_FIRST: 'Please select a city first',
        SELECT_AREA: 'Select Area',
        NO_AREAS_AVAILABLE: 'No areas available. Please select a city first.',
        NO_AREAS_FOR_CITY: 'No areas available for this city',
        SAVE_LOCATION: 'Save Location',
        SELECT_SPECIALIZATIONS: 'Select Specializations',
        CHOOSE_SPECIALIZATION_TREE: 'Choose from the specialization tree:',
        SEARCH_SPECIALIZATIONS: 'Search specializations...',
        INDUSTRIAL: 'Industrial',
        COMMERCIAL: 'Commercial',
        RESIDENTIAL: 'Residential',
        NATIONAL_PROJECT: 'National Project',
        SELECT_SCOPES_TYPES: 'Select scopes and types. Selecting a scope will select all types under it.',
        SAVE: 'Save'
      }
    },
    HOME: {
      NAVIGATION: {
        HOME: 'Home',
        ABOUT_US: 'About Us',
        ADVERTISEMENTS: 'Advertisements',
        CONTACT_US: 'Contact Us',
        REGISTER_GUEST: 'Register Guest',
        LANGUAGE: 'English',
        ARABIC: 'العربية'
      },
      HERO: {
        EASY: 'Easy',
        SPEED: 'Speed',
        RELIABILITY: 'Reliability'
      },
      PROPERTIES: {
        TITLE: 'Featured Properties',
        LOADING: 'Loading...',
        LOAD_MORE: 'Load More Properties',
        ROOMS: 'Rooms',
        BATHROOMS: 'Bathrooms',
        AREA: 'Area',
        TYPE: 'Type',
        RATING: '5.0'
      },
      ALL_PROPERTIES: {
        TITLE: 'All Properties',
        SUBTITLE: 'Discover your perfect property from our extensive collection',
        BACK_TO_HOME: 'Back to Home',
        SEARCH_PLACEHOLDER: 'Search properties by type, location, or features...',
        VIEW_DETAILS: 'View Details',
        LOAD_MORE: 'Load More Properties',
        LOADING_MORE: 'Loading more...',
        ALL_LOADED: 'You\'ve seen all available properties!',
        NO_PROPERTIES_FOUND: 'No Properties Found',
        NO_PROPERTIES_MESSAGE: 'No properties match your search criteria. Try adjusting your search terms.',
        NO_PROPERTIES_AVAILABLE: 'No Properties Available',
        NO_PROPERTIES_AVAILABLE_MESSAGE: 'Sorry, we couldn\'t find any properties at the moment.',
        LOGIN_REQUIRED: 'Please Login',
        LOGIN_MESSAGE: 'You need to login to view property details',
        LOGIN_ROLE_MESSAGE: 'You need to have a valid role to view property details',
        INVALID_SESSION: 'Invalid session. Please login again',
        LOGIN_BUTTON: 'Login',
        CANCEL_BUTTON: 'Cancel'
      },
      LOCATIONS: {
        TITLE: 'Explore Locations',
        PROPERTIES_AVAILABLE: 'Properties Available'
      },
      ARTICLES: {
        TITLE: 'Articles That Interest You',
        ALL_ARTICLES: 'All Articles',
        ARTICLE_1: {
          TITLE: 'Modern Finishing Materials - Shop with the Best',
          DESCRIPTION: 'A very quiet area away from the noise and hustle of the city, suitable for large and small families, spacious area with a private garden.'
        },
        ARTICLE_2: {
          TITLE: 'Invest Your Money with Hotel Property',
          DESCRIPTION: 'Excellent investment opportunity in the heart of the city, guaranteed returns and integrated management, strategic location near the airport and commercial centers.'
        },
        ARTICLE_3: {
          TITLE: 'Villa 6 October April 2019',
          DESCRIPTION: 'Latest international finishing materials, high quality and competitive prices, specialized team to implement finishing works to the highest standards.'
        },
        ARTICLE_4: {
          TITLE: 'Apartment in New Cairo',
          DESCRIPTION: 'Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated facilities, close to universities and international schools.'
        },
        ARTICLE_5: {
          TITLE: 'North Coast Properties',
          DESCRIPTION: 'Residential units directly on the sea, wonderful panoramic view, integrated recreational facilities and suitable for summer investment.'
        },
        ARTICLE_6: {
          TITLE: 'Administrative Offices Downtown',
          DESCRIPTION: 'Modern office spaces in the heart of Cairo, suitable for companies and institutions, parking and integrated service facilities.'
        },
        ARTICLE_7: {
          TITLE: 'Luxury Villas in Sheikh Zayed',
          DESCRIPTION: 'Exclusive villas with modern design, private gardens, and premium finishes in the heart of Sheikh Zayed City.'
        },
        ARTICLE_8: {
          TITLE: 'Commercial Spaces in Maadi',
          DESCRIPTION: 'Prime commercial locations in Maadi with high foot traffic, perfect for retail businesses and restaurants.'
        },
        ARTICLE_9: {
          TITLE: 'Investment Opportunities in Heliopolis',
          DESCRIPTION: 'High-yield investment properties in Heliopolis with guaranteed rental income and strategic locations.'
        }
      },
      PROPERTY_OPERATIONS: {
        SELL: 'Sell',
        PURCHASING: 'Purchasing',
        RENT_IN: 'Rent In',
        RENT_OUT: 'Rent Out'
      },
      CURRENCY: {
        EGP: 'EGP'
      },
      UNITS: {
        SQM: 'sqm'
      },
      PROPERTY_TYPES: {
        APARTMENT: 'Apartment',
        DUPLEX: 'Duplex',
        PENTHOUSE: 'Penthouse',
        ROOF: 'Roof',
        STUDIO: 'Studio',
        BASEMENT: 'Basement',
        'FULL BUILDING': 'Full Building',
        'FULL VILLA': 'Full Villa',
        'ADMINISTRATIVE UNIT': 'Administrative Unit',
        'MEDICAL CLINIC': 'Medical Clinic',
        PHARMACY: 'Pharmacy',
        'COMMERCIAL SHOP': 'Commercial Shop',
        'RESIDENTIAL LAND': 'Residential Land',
        'COMMERCIAL ADMINISTRATIVE LAND': 'Commercial Administrative Land',
        'ADMINISTRATIVE BUILDING': 'Administrative Building',
        'COMMERCIAL BUILDING': 'Commercial Building',
        'ADMINISTRATIVE COMMERCIAL BUILDING': 'Administrative Commercial Building',
        FACTORY: 'Factory',
        'INDUSTRIAL LAND': 'Industrial Land',
        WAREHOUSE: 'Warehouse',
        'WAREHOUSE LAND': 'Warehouse Land',
        'I-VILLA': 'I-Villa',
        'TWIN HOUSE': 'Twin House',
        'TOWN HOUSE': 'Town House',
        'STANDALONE VILLA': 'Standalone Villa',
        'SUMMER CHALET': 'Summer Chalet'
      },
      DOWNLOAD_APP: {
        TITLE: 'Download the Electronic App',
        SUBTITLE: 'Download our app to access the latest real estate offers and properties',
        APP_STORE: 'App Store',
        GOOGLE_PLAY: 'Google Play',
        DOWNLOAD_ON: 'Download on the',
        GET_IT_ON: 'GET IT ON',
        APP_NAME: 'EASY DEAL',
        APP_DESCRIPTION: 'Your trusted real estate partner'
      },
      NEWSLETTER: {
        TITLE: 'Join Our Mailing List',
        SUBTITLE: 'Get latest offers',
        PLACEHOLDER: 'Your email',
        SUBSCRIBE: 'Subscribe'
      },
      FOOTER: {
        COMPANY_DESCRIPTION: 'Your trusted real estate partner in Egypt. We provide the best properties and investment opportunities with professional service.',
        QUICK_LINKS: 'Quick Links',
        SERVICES: 'Services',
        CONTACT_INFO: 'Contact Information',
        PHONE: '19888 - <EMAIL>',
        EMAIL: '<EMAIL>',
        ADDRESS: 'Cairo, Egypt',
        COPYRIGHT: '© 2025 EasyDeal. All rights reserved.',
        PRIVACY_POLICY: 'Privacy Policy',
        TERMS_OF_SERVICE: 'Terms of Service',
        COOKIE_POLICY: 'Cookie Policy',
        LINKS: {
          HOME: 'Home',
          ABOUT: 'About EasyDeal',
          NEW_PROJECTS: 'New Projects',
          ADVERTISEMENTS: 'Advertisements',
          CONTACT: 'Contact Us'
        },
        SERVICES_LIST: {
          PROPERTY_SEARCH: 'Property Search',
          INVESTMENT_CONSULTING: 'Investment Consulting',
          PROPERTY_MANAGEMENT: 'Property Management',
          LEGAL_SUPPORT: 'Legal Support',
          FINANCING_OPTIONS: 'Financing Options'
        }
      }
    },
    USER_MENU: {
      DASHBOARD: 'Dashboard',
      MESSAGES: 'Messages',
      MY_PROFILE: 'My Profile',
      NEW_REQUEST: 'New Request',
      REQUESTS: 'Requests',
      HELP: 'Help',
      NOTIFICATIONS: 'Notifications',
      LOGOUT: 'Logout'
    },
    COMMON: {
      ERROR: 'Error!',
      SUCCESS: 'Success!',
      OK: 'OK',
      ERRORS: {
        FAILED_TO_LOAD_PROFILE: 'Failed to load profile data.'
      }
    },
    ROLES: {
      ADMIN: 'Admin',
      BROKER: 'Broker',
      CLIENT: 'Client',
      DEVELOPER: 'Developer',
      USER: 'User'
    },
    ACCOUNT_TYPES: {
      FREE: 'Free',
      PREMIUM: 'Premium',
      BASIC: 'Basic',
      GOLD: 'Gold',
      SILVER: 'Silver',
      BRONZE: 'Bronze'
    },
    ECOMMERCE: {
      COMMON: {
        SELECTED_RECORDS_COUNT: 'Selected records count: ',
        ALL: 'All',
        SUSPENDED: 'Suspended',
        ACTIVE: 'Active',
        FILTER: 'Filter',
        BY_STATUS: 'by Status',
        BY_TYPE: 'by Type',
        BUSINESS: 'Business',
        INDIVIDUAL: 'Individual',
        SEARCH: 'Search',
        IN_ALL_FIELDS: 'in all fields'
      },
      ECOMMERCE: 'eCommerce',
      CUSTOMERS: {
        CUSTOMERS: 'Customers',
        CUSTOMERS_LIST: 'Customers list',
        NEW_CUSTOMER: 'New Customer',
        DELETE_CUSTOMER_SIMPLE: {
          TITLE: 'Customer Delete',
          DESCRIPTION: 'Are you sure to permanently delete this customer?',
          WAIT_DESCRIPTION: 'Customer is deleting...',
          MESSAGE: 'Customer has been deleted'
        },
        DELETE_CUSTOMER_MULTY: {
          TITLE: 'Customers Delete',
          DESCRIPTION: 'Are you sure to permanently delete selected customers?',
          WAIT_DESCRIPTION: 'Customers are deleting...',
          MESSAGE: 'Selected customers have been deleted'
        },
        UPDATE_STATUS: {
          TITLE: 'Status has been updated for selected customers',
          MESSAGE: 'Selected customers status have successfully been updated'
        },
        EDIT: {
          UPDATE_MESSAGE: 'Customer has been updated',
          ADD_MESSAGE: 'Customer has been created'
        }
      }
    }
  }
};
