/* You can add global styles to this file, and also import other style files */

/* RTL Support */
html[dir="rtl"] {
  direction: rtl;
  text-align: right;
  overflow-x: hidden !important; /* منع الـ scroll الأفقي */
}

html[lang="ar"] {
  overflow-x: hidden !important; /* منع الـ scroll الأفقي */
}

html[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* منع الـ scroll الأفقي في العربية */
body.rtl,
html[dir="rtl"] body,
html[lang="ar"] body {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}

/* إصلاح أيقونات Bootstrap في العربية */
html[dir="rtl"] .btn-icon,
html[lang="ar"] .btn-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

html[dir="rtl"] .btn-icon i,
html[lang="ar"] .btn-icon i {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* محاذاة الأرقام في العربية - تحسين شامل */
html[dir="rtl"] .fs-2.fw-bolder,
html[lang="ar"] .fs-2.fw-bolder {
  text-align: center !important;
  direction: rtl !important;
  font-family: 'Arial', sans-serif !important;
  display: block !important;
  width: 100% !important;
}

html[dir="rtl"] .border-dashed,
html[lang="ar"] .border-dashed {
  text-align: center !important;

  .d-flex.align-items-center {
    justify-content: center !important;
    text-align: center !important;
  }

  .fw-bold.fs-6 {
    text-align: center !important;
  }
}

/* تحسين عرض الأرقام العربية في جميع أنحاء التطبيق */
html[dir="rtl"] .text-center,
html[lang="ar"] .text-center {
  text-align: center !important;
}

html[dir="rtl"] .w-100,
html[lang="ar"] .w-100 {
  width: 100% !important;
}

/* إصلاح أيقونة الكاميرا في العربية */
html[dir="rtl"] .btn-circle,
html[lang="ar"] .btn-circle {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

html[dir="rtl"] .btn-circle i,
html[lang="ar"] .btn-circle i {
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* تحسين التخطيط في العربية */
html[dir="rtl"] .d-flex.justify-content-between,
html[lang="ar"] .d-flex.justify-content-between {
  .d-flex.my-4 {
    margin-left: 0 !important;
    margin-right: auto !important;
  }
}

/* محاذاة الاسم والمعلومات لليسار في العربية */
html[dir="rtl"] .d-flex.flex-column .d-flex.align-items-center.mb-2,
html[lang="ar"] .d-flex.flex-column .d-flex.align-items-center.mb-2 {
  justify-content: flex-start !important;
  text-align: left !important;
  direction: ltr !important;
  width: 100% !important;
}

html[dir="rtl"] .d-flex.flex-column .d-flex.flex-wrap,
html[lang="ar"] .d-flex.flex-column .d-flex.flex-wrap {
  justify-content: flex-start !important;
  text-align: left !important;
  direction: ltr !important;
}

html[dir="rtl"] .text-gray-800.fs-2.fw-bolder,
html[lang="ar"] .text-gray-800.fs-2.fw-bolder {
  text-align: left !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
}

html[dir="rtl"] .badge.fw-bolder,
html[lang="ar"] .badge.fw-bolder {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* إصلاح محاذاة الاسم في العربية - أكثر تحديداً */
html[dir="rtl"] .card-body .d-flex.flex-column .d-flex.align-items-center.mb-2,
html[lang="ar"] .card-body .d-flex.flex-column .d-flex.align-items-center.mb-2 {
  justify-content: flex-start !important;
  align-items: flex-start !important;
  text-align: left !important;
  direction: ltr !important;
}

html[dir="rtl"] .card-body .text-gray-800.fs-2.fw-bolder.me-1,
html[lang="ar"] .card-body .text-gray-800.fs-2.fw-bolder.me-1 {
  text-align: left !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
}

/* ستايلات محددة للـ classes الجديدة */
html[dir="rtl"] .name-section,
html[lang="ar"] .name-section {
  justify-content: flex-end !important;
  align-items: flex-start !important;
  text-align: right !important;
  direction: rtl !important;
  width: 100% !important;
}

html[dir="rtl"] .user-name,
html[lang="ar"] .user-name {
  text-align: right !important;
  margin-right: 0 !important;
  margin-left: 0 !important;
}

html[dir="rtl"] .user-badge,
html[lang="ar"] .user-badge {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

/* إضافة ستايلات أكثر تحديداً للاسم في العربية */
html[dir="rtl"] .card-body .name-section .user-name,
html[lang="ar"] .card-body .name-section .user-name {
  text-align: right !important;
  direction: rtl !important;
  width: 100% !important;
  display: block !important;
}

html[dir="rtl"] .card-body .name-section,
html[lang="ar"] .card-body .name-section {
  justify-content: flex-end !important;
  text-align: right !important;
  direction: rtl !important;
  width: 100% !important;
}

/* محاذاة الاسم والمعلومات لليسار في العربية */
html[dir="rtl"] .d-flex.flex-column .d-flex.align-items-center.mb-2,
html[lang="ar"] .d-flex.flex-column .d-flex.align-items-center.mb-2 {
  justify-content: flex-start !important;
  text-align: left !important;
  direction: ltr !important;
}

html[dir="rtl"] .d-flex.flex-column .d-flex.flex-wrap,
html[lang="ar"] .d-flex.flex-column .d-flex.flex-wrap {
  justify-content: flex-start !important;
  text-align: left !important;
  direction: ltr !important;
}

html[dir="rtl"] .text-gray-800.fs-2.fw-bolder,
html[lang="ar"] .text-gray-800.fs-2.fw-bolder {
  text-align: left !important;
}

html[dir="rtl"] .badge.fw-bolder,
html[lang="ar"] .badge.fw-bolder {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* RTL Support - تعديلات محدودة جداً */
html[dir="rtl"] {
  /* فقط تعديل اتجاه النص */
  .form-control {
    text-align: right;
  }

  /* تعديل القوائم المنسدلة */
  .dropdown-menu,
  .language-dropdown {
    right: 0;
    left: auto;
  }
}

/* تعديلات RTL محددة للمكونات فقط */
.rtl {
  /* تعديل الهوامش بحذر شديد */
  .me-2:not(.keep-margin) {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .me-3:not(.keep-margin) {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }

  .ms-2:not(.keep-margin) {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  /* إصلاح Download App Section */
  .download-app-section {
    .app-store-buttons {
      flex-direction: row-reverse;
    }

    .app-preview {
      text-align: center !important;
    }

    .app-info {
      text-align: center !important;
      margin-top: 1rem !important;
    }

    .app-name, .app-description {
      text-align: center !important;
    }
  }

  /* إصلاح Footer */
  .footer-section {
    .footer-menu {
      text-align: right;
      padding-left: 0 !important;
      margin-left: 0 !important;
    }

    .footer-bottom-links {
      text-align: right !important;
    }

    .footer-links {
      text-align: right !important;
    }

    .footer-contact {
      text-align: right !important;
    }

    .contact-item {
      text-align: right !important;
      direction: rtl !important;
    }
  }

  /* إصلاح أيقونات المتاجر في RTL */
  .store-button {
    .store-icon {
      padding: 0 !important;
      margin: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .store-icon i {
      padding: 0 !important;
      margin: 0 !important;
    }
  }

  /* حماية الأيقونات من التأثر بـ RTL */
  .fas, .fab, .far, .fal, .fad {
    display: inline-block !important;
  }

  /* حماية الألوان والديزاين */
  .btn, .card, .navbar, .dropdown-menu {
    /* الحفاظ على الألوان والتصميم الأصلي */
  }
}

/* Arabic font support */
html[lang="ar"] {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;

  /* تعديل موقع النصوص في Download App Section */
  .app-preview {
    text-align: center !important;
  }

  .app-info {
    text-align: center !important;
    margin-top: 1rem !important;
  }

  .app-name, .app-description {
    text-align: center !important;
  }

  /* إصلاح أيقونات المتاجر */
  .store-icon {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .store-icon i {
    padding: 0 !important;
    margin: 0 !important;
    font-size: 1rem !important; /* تصغير الأيقونات أكثر */
    width: auto !important;
    height: auto !important;
    transform: scale(0.8) !important; /* تصغير إضافي */
  }

  /* إصلاح Newsletter */
  .text-center.text-md-start {
    text-align: right !important;
  }

  .newsletter-title,
  .newsletter-subtitle {
    text-align: right !important;
  }

  /* إصلاح Footer إضافي */
  .footer-menu {
    text-align: right !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    list-style: none !important;
  }

  .footer-menu li {
    text-align: right !important;
    margin-bottom: 0.5rem !important;
  }

  .footer-menu li a {
    text-align: right !important;
    display: block !important;
  }

  .footer-links {
    text-align: right !important;
  }

  .footer-contact {
    text-align: right !important;
  }

  .contact-item {
    text-align: right !important;
    direction: rtl !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
  }

  .contact-item i {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
}

/* Smooth transitions for direction changes */
* {
  transition: margin 0.3s ease, padding 0.3s ease;
}

/* تعديل إضافي لـ Download App Section في العربية */
html[dir="rtl"] .app-preview {
  text-align: center !important;
}

html[dir="rtl"] .app-info {
  text-align: center !important;
  margin-top: 1rem !important;
}

html[dir="rtl"] .app-name,
html[dir="rtl"] .app-description {
  text-align: center !important;
}

/* تحريك Download App Section لأقصى الشمال في العربية */
html[dir="rtl"] .col-lg-6.d-flex.justify-content-end,
html[lang="ar"] .col-lg-6.d-flex.justify-content-end {
  justify-content: flex-start !important;
}

html[dir="rtl"] .app-preview.text-end,
html[lang="ar"] .app-preview.text-end {
  text-align: left !important;
}

html[dir="rtl"] .app-logo-container,
html[lang="ar"] .app-logo-container {
  text-align: left !important;
}

html[dir="rtl"] .app-info,
html[lang="ar"] .app-info {
  text-align: left !important;
}

/* إصلاح إضافي للـ Download App Section */
html[dir="rtl"] .download-app-section .col-lg-6:last-child,
html[lang="ar"] .download-app-section .col-lg-6:last-child {
  order: -1 !important;
}

html[dir="rtl"] .download-app-section .row,
html[lang="ar"] .download-app-section .row {
  flex-direction: row-reverse !important;
}

/* إصلاح أيقونة السايد بار toggle في العربية */
html[dir="rtl"] #kt_app_sidebar_toggle,
html[lang="ar"] #kt_app_sidebar_toggle {
  left: 0 !important;
  right: auto !important;
  transform: translateX(-50%) translateY(-50%) !important;
}

html[dir="rtl"] #kt_app_sidebar_toggle app-keenicon,
html[lang="ar"] #kt_app_sidebar_toggle app-keenicon {
  transform: rotate(0deg) !important;
}

html[dir="rtl"] #kt_app_sidebar_toggle .ki-double-left,
html[lang="ar"] #kt_app_sidebar_toggle .ki-double-left {
  transform: rotate(0deg) !important;
}

/* إصلاح موقع toggle button في العربية */
html[dir="rtl"] .app-sidebar-toggle,
html[lang="ar"] .app-sidebar-toggle {
  left: 0 !important;
  right: auto !important;
  transform: translateX(-50%) translateY(-50%) !important;
}

/* إصلاح إضافي للـ toggle button */
html[dir="rtl"] .app-sidebar-toggle.btn,
html[lang="ar"] .app-sidebar-toggle.btn {
  left: 0 !important;
  right: auto !important;
}

html[dir="rtl"] .start-100,
html[lang="ar"] .start-100 {
  left: 0 !important;
  right: auto !important;
}

/* إصلاح الـ rotate classes في العربية */
html[dir="rtl"] .rotate-180,
html[lang="ar"] .rotate-180 {
  transform: rotate(0deg) !important;
}

html[dir="rtl"] .rotate,
html[lang="ar"] .rotate {
  transform: none !important;
}

/* إصلاح الـ translate-middle في العربية */
html[dir="rtl"] .translate-middle,
html[lang="ar"] .translate-middle {
  transform: translateX(50%) translateY(-50%) !important;
}

/* إصلاح الـ position classes في العربية */
html[dir="rtl"] .position-absolute.start-100,
html[lang="ar"] .position-absolute.start-100 {
  left: 0 !important;
  right: auto !important;
}

/* إصلاح محدد للسايد بار toggle */
html[dir="rtl"] [data-kt-toggle-target="body"],
html[lang="ar"] [data-kt-toggle-target="body"] {
  left: 0 !important;
  right: auto !important;
}

/* إصلاح المحتوى الرئيسي في العربية */
html[dir="rtl"] .app-content,
html[lang="ar"] .app-content {
  text-align: right !important;
  direction: rtl !important;
}

html[dir="rtl"] .app-container,
html[lang="ar"] .app-container {
  direction: rtl !important;
}

/* إصلاح الكروت في العربية */
html[dir="rtl"] .card,
html[lang="ar"] .card {
  text-align: right !important;
  direction: rtl !important;
}

html[dir="rtl"] .card-header,
html[lang="ar"] .card-header {
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .card-title,
html[lang="ar"] .card-title {
  text-align: right !important;
}

html[dir="rtl"] .card-toolbar,
html[lang="ar"] .card-toolbar {
  order: -1 !important;
}

/* إصلاح الأزرار في العربية */
html[dir="rtl"] .btn,
html[lang="ar"] .btn {
  text-align: center !important;
}

html[dir="rtl"] .btn i,
html[lang="ar"] .btn i {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* إصلاح الـ rows والـ columns في العربية */
html[dir="rtl"] .row,
html[lang="ar"] .row {
  direction: rtl !important;
}

html[dir="rtl"] .col,
html[dir="rtl"] [class*="col-"],
html[lang="ar"] .col,
html[lang="ar"] [class*="col-"] {
  text-align: right !important;
}

/* إصلاح الـ flexbox في العربية */
html[dir="rtl"] .d-flex,
html[lang="ar"] .d-flex {
  direction: rtl !important;
}

html[dir="rtl"] .justify-content-between,
html[lang="ar"] .justify-content-between {
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .align-items-center,
html[lang="ar"] .align-items-center {
  text-align: right !important;
}

/* إصلاح الـ badges في العربية */
html[dir="rtl"] .badge,
html[lang="ar"] .badge {
  text-align: center !important;
  direction: rtl !important;
}

/* إصلاح الـ text alignment في العربية */
html[dir="rtl"] .text-center,
html[lang="ar"] .text-center {
  text-align: center !important;
}

html[dir="rtl"] .text-start,
html[lang="ar"] .text-start {
  text-align: right !important;
}

html[dir="rtl"] .text-end,
html[lang="ar"] .text-end {
  text-align: left !important;
}

/* إصلاح الـ dashboard cards في العربية */
html[dir="rtl"] .card-custom,
html[lang="ar"] .card-custom {
  text-align: right !important;
  direction: rtl !important;
}

html[dir="rtl"] .card-label,
html[lang="ar"] .card-label {
  text-align: right !important;
  flex-direction: row-reverse !important;
  display: flex !important;
  align-items: center !important;
}

html[dir="rtl"] .card-label svg,
html[dir="rtl"] .card-label i,
html[lang="ar"] .card-label svg,
html[lang="ar"] .card-label i {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

/* إصلاح الـ symbols في العربية */
html[dir="rtl"] .symbol,
html[lang="ar"] .symbol {
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* إصلاح الـ profile section في العربية */
html[dir="rtl"] .text-sm-start,
html[lang="ar"] .text-sm-start {
  text-align: right !important;
}

html[dir="rtl"] .mx-sm-0,
html[lang="ar"] .mx-sm-0 {
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* إصلاح الـ margins في العربية */
html[dir="rtl"] .me-1,
html[dir="rtl"] .me-2,
html[dir="rtl"] .me-3,
html[lang="ar"] .me-1,
html[lang="ar"] .me-2,
html[lang="ar"] .me-3 {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}

html[dir="rtl"] .ms-2,
html[dir="rtl"] .ms-3,
html[lang="ar"] .ms-2,
html[lang="ar"] .ms-3 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

/* إصلاح الـ text wrapping في العربية */
html[dir="rtl"] .flex-wrap,
html[lang="ar"] .flex-wrap {
  direction: rtl !important;
}

/* إصلاح الـ FontAwesome icons في العربية */
html[dir="rtl"] .fa-solid,
html[dir="rtl"] .fa-regular,
html[lang="ar"] .fa-solid,
html[lang="ar"] .fa-regular {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}

/* إصلاح أيقونات المتاجر في العربية */
html[dir="rtl"] .store-icon {
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

html[dir="rtl"] .store-icon i {
  padding: 0 !important;
  margin: 0 !important;
}

/* إصلاح Newsletter في العربية */
html[dir="rtl"] .text-center.text-md-start {
  text-align: right !important;
}

html[dir="rtl"] .newsletter-title,
html[dir="rtl"] .newsletter-subtitle {
  text-align: left !important;
}

/* إصلاح Newsletter في العربية - تخطيط محدد */
html[dir="rtl"] .col-md-7.text-center.text-md-start,
html[lang="ar"] .col-md-7.text-center.text-md-start {
  text-align: right !important;
  justify-self: flex-end !important;
}

html[dir="rtl"] .newsletter-title,
html[dir="rtl"] .newsletter-subtitle,
html[lang="ar"] .newsletter-title,
html[lang="ar"] .newsletter-subtitle {
  text-align: center !important;
  margin: 0 auto !important;
}

/* إصلاح Input Group في العربية */
html[dir="rtl"] .input-group,
html[lang="ar"] .input-group {
  justify-self: center !important;
  margin: 0 auto !important;
  width: 100% !important;
}

html[dir="rtl"] .input-group .form-control,
html[lang="ar"] .input-group .form-control {
  text-align: right !important;
  border-radius: 0.375rem 0 0 0.375rem !important;
}

html[dir="rtl"] .input-group .btn-subscribe,
html[lang="ar"] .input-group .btn-subscribe {
  border-radius: 0 0.375rem 0.375rem 0 !important;
}

/* إصلاح إضافي للـ Newsletter في العربية */
html[dir="rtl"] .newsletter-container .row,
html[lang="ar"] .newsletter-container .row {
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .newsletter-container .col-md-5,
html[lang="ar"] .newsletter-container .col-md-5 {
  display: flex !important;
  justify-content: flex-start !important;
}

html[dir="rtl"] .newsletter-container .col-md-7,
html[lang="ar"] .newsletter-container .col-md-7 {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
}

/* إصلاح الـ media queries للـ Newsletter */
@media (max-width: 767.98px) {
  html[dir="rtl"] .newsletter-container .col-md-7,
  html[lang="ar"] .newsletter-container .col-md-7 {
    align-items: center !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
  }

  html[dir="rtl"] .newsletter-title,
  html[dir="rtl"] .newsletter-subtitle,
  html[lang="ar"] .newsletter-title,
  html[lang="ar"] .newsletter-subtitle {
    text-align: center !important;
  }
}

/* إصلاح محدد للـ Newsletter Classes الجديدة */
html[dir="rtl"] .newsletter-row,
html[lang="ar"] .newsletter-row {
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .newsletter-content,
html[lang="ar"] .newsletter-content {
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

html[dir="rtl"] .newsletter-form-wrapper,
html[lang="ar"] .newsletter-form-wrapper {
  display: flex !important;
  justify-content: center !important;
}

html[dir="rtl"] .newsletter-input-group,
html[lang="ar"] .newsletter-input-group {
  direction: ltr !important;
  width: 100% !important;
}

/* إصلاح إضافي للـ Newsletter Input Group */
html[dir="rtl"] .newsletter-form,
html[lang="ar"] .newsletter-form {
  width: 100% !important;
  display: flex !important;
  justify-content: flex-start !important;
}

html[dir="rtl"] .newsletter-form .input-group,
html[lang="ar"] .newsletter-form .input-group {
  width: 100% !important;
}

/* إصلاح الـ button في العربية */
html[dir="rtl"] .btn-subscribe,
html[lang="ar"] .btn-subscribe {
  border-radius: 0.375rem 0 0 0.375rem !important;
}

html[dir="rtl"] .newsletter-form .form-control,
html[lang="ar"] .newsletter-form .form-control {
  border-radius: 0 0.375rem 0.375rem 0 !important;
}

/* إصلاح Property Cards في العربية */
html[dir="rtl"] .property-badge,
html[lang="ar"] .property-badge {
  right: 10px !important;
  left: auto !important;
}

html[dir="rtl"] .property-location,
html[lang="ar"] .property-location {
  right: 10px !important;
  left: auto !important;
  text-align: right !important;
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .property-location i,
html[lang="ar"] .property-location i {
  margin-left: 0.25rem !important;
  margin-right: 0 !important;
}

html[dir="rtl"] .property-content,
html[lang="ar"] .property-content {
  text-align: right !important;
  direction: rtl !important;
}

html[dir="rtl"] .property-title,
html[lang="ar"] .property-title {
  text-align: right !important;
}

html[dir="rtl"] .property-price,
html[lang="ar"] .property-price {
  text-align: right !important;
}

/* إصلاح Footer في RTL */
html[dir="rtl"] .footer-menu {
  text-align: right !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
  list-style: none !important;
}

html[dir="rtl"] .footer-menu li {
  text-align: right !important;
  margin-bottom: 0.5rem !important;
}

html[dir="rtl"] .footer-menu li a {
  text-align: right !important;
  display: block !important;
}

html[dir="rtl"] .footer-links {
  text-align: right !important;
}

html[dir="rtl"] .footer-contact {
  text-align: right !important;
}

html[dir="rtl"] .contact-item {
  text-align: right !important;
  direction: rtl !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

html[dir="rtl"] .contact-item i {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

html[dir="rtl"] .footer-title {
  text-align: right !important;
}

/* إخفاء ::after في العربية */
html[dir="rtl"] .footer-title::after,
html[lang="ar"] .footer-title::after,
.rtl .footer-title::after {
  display: none !important;
}

/* إخفاء جميع ::after elements في العربية */
html[dir="rtl"] h6::after,
html[lang="ar"] h6::after,
.rtl h6::after {
  display: none !important;
}

html[dir="rtl"] .footer-links h6::after,
html[lang="ar"] .footer-links h6::after,
.rtl .footer-links h6::after {
  display: none !important;
}

/* تحريك السايد بار لليمين في العربية */
html[dir="rtl"] .app-sidebar,
html[lang="ar"] .app-sidebar {
  right: 0 !important;
  left: auto !important;
}

html[dir="rtl"] .app-sidebar-primary,
html[lang="ar"] .app-sidebar-primary {
  right: 0 !important;
  left: auto !important;
}

/* تعديل المحتوى الرئيسي في العربية - حل محسن بدون scroll bar */
html[dir="rtl"] .app-main,
html[lang="ar"] .app-main {
  margin-right: 0 !important; /* إزالة الهامش الأيمن */
  margin-left: 0 !important;
  width: 100% !important; /* استخدام 100% بدلاً من 100vw */
  max-width: 100% !important;
  padding-right: var(--bs-app-sidebar-width) !important; /* إضافة padding بدلاً من margin */
  box-sizing: border-box !important;
  overflow-x: hidden !important; /* منع الـ scroll الأفقي */
}

html[dir="rtl"] .app-wrapper,
html[lang="ar"] .app-wrapper {
  flex-direction: row-reverse !important;
  width: 100% !important; /* استخدام 100% بدلاً من 100vw */
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* إصلاح إضافي للسايد بار في العربية */
html[dir="rtl"] .app-container,
html[lang="ar"] .app-container {
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .app-sidebar-wrapper,
html[lang="ar"] .app-sidebar-wrapper {
  order: 2 !important;
}

html[dir="rtl"] .app-main-wrapper,
html[lang="ar"] .app-main-wrapper {
  order: 1 !important;
  width: 100% !important; /* استخدام 100% بدلاً من 100vw */
  max-width: 100% !important;
  flex: 1 !important;
  overflow-x: hidden !important; /* منع الـ scroll الأفقي */
}

/* تعديل الـ borders في العربية */
html[dir="rtl"] .app-sidebar {
  border-left: none !important;
  border-right: 1px solid var(--bs-border-color) !important;
}

html[dir="rtl"] .app-sidebar-primary {
  border-left: none !important;
  border-right: 1px solid var(--bs-border-color) !important;
}

/* إصلاح السايد بار في الموبايل للعربية */
@media (max-width: 991.98px) {
  html[dir="rtl"] .app-sidebar,
  html[lang="ar"] .app-sidebar {
    right: 0 !important;
    left: auto !important;
    transform: translateX(100%) !important;
  }

  html[dir="rtl"] .app-sidebar.show,
  html[lang="ar"] .app-sidebar.show {
    transform: translateX(0) !important;
  }

  html[dir="rtl"] .app-sidebar-primary,
  html[lang="ar"] .app-sidebar-primary {
    right: 0 !important;
    left: auto !important;
  }

  // إصلاح المحتوى في الموبايل للعربية
  html[dir="rtl"] .app-main,
  html[lang="ar"] .app-main {
    padding-right: 0 !important; // إزالة padding في الموبايل
    width: 100% !important;
    margin: 0 !important;
  }
}

/* تعديل الـ body layout في العربية */
html[dir="rtl"] body,
html[lang="ar"] body {
  direction: rtl !important;
}

html[dir="rtl"] .d-flex,
html[lang="ar"] .d-flex {
  flex-direction: row-reverse;
}

/* إصلاح الـ menu items في السايد بار */
html[dir="rtl"] .menu-item,
html[lang="ar"] .menu-item {
  text-align: right !important;
}

html[dir="rtl"] .menu-link,
html[lang="ar"] .menu-link {
  text-align: right !important;
  flex-direction: row-reverse !important;
}

html[dir="rtl"] .menu-icon,
html[lang="ar"] .menu-icon {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

/* إصلاح أيقونات السايد بار في العربية */
html[dir="rtl"] .menu-title,
html[lang="ar"] .menu-title {
  order: 1 !important;
  text-align: right !important;
}

html[dir="rtl"] .menu-arrow,
html[lang="ar"] .menu-arrow {
  order: 0 !important;
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
  transform: rotate(180deg) !important;
}

/* إصلاح أيقونات FontAwesome في السايد بار */
html[dir="rtl"] .menu-icon i,
html[lang="ar"] .menu-icon i {
  transform: scaleX(-1) !important;
}

/* عكس الأيقونات فقط في السايد بار */
html[dir="rtl"] .app-sidebar .fas,
html[lang="ar"] .app-sidebar .fas {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar .fab,
html[lang="ar"] .app-sidebar .fab {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar-primary .fas,
html[lang="ar"] .app-sidebar-primary .fas {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar-primary .fab,
html[lang="ar"] .app-sidebar-primary .fab {
  transform: scaleX(-1) !important;
}

/* إصلاح محدد للأيقونات في السايد بار */
html[dir="rtl"] .app-sidebar .menu-icon,
html[lang="ar"] .app-sidebar .menu-icon {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

html[dir="rtl"] .app-sidebar .menu-icon i,
html[lang="ar"] .app-sidebar .menu-icon i {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar-primary .menu-icon,
html[lang="ar"] .app-sidebar-primary .menu-icon {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

html[dir="rtl"] .app-sidebar-primary .menu-icon i,
html[lang="ar"] .app-sidebar-primary .menu-icon i {
  transform: scaleX(-1) !important;
}

/* إصلاح أيقونات محددة */
html[dir="rtl"] .app-sidebar .fa-home,
html[dir="rtl"] .app-sidebar .fa-user,
html[dir="rtl"] .app-sidebar .fa-cog,
html[dir="rtl"] .app-sidebar .fa-chart-bar,
html[lang="ar"] .app-sidebar .fa-home,
html[lang="ar"] .app-sidebar .fa-user,
html[lang="ar"] .app-sidebar .fa-cog,
html[lang="ar"] .app-sidebar .fa-chart-bar {
  transform: scaleX(-1) !important;
}

/* عكس جميع أيقونات FontAwesome في السايد بار */
html[dir="rtl"] .app-sidebar [class*="fa-"],
html[lang="ar"] .app-sidebar [class*="fa-"] {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar-primary [class*="fa-"],
html[lang="ar"] .app-sidebar-primary [class*="fa-"] {
  transform: scaleX(-1) !important;
}

/* عكس أيقونات SVG أيضاً */
html[dir="rtl"] .app-sidebar svg,
html[lang="ar"] .app-sidebar svg {
  transform: scaleX(-1) !important;
}

html[dir="rtl"] .app-sidebar-primary svg,
html[lang="ar"] .app-sidebar-primary svg {
  transform: scaleX(-1) !important;
}

/* استثناءات - عدم عكس أيقونات الهوم بيج */
html[dir="rtl"] .navbar .fas,
html[dir="rtl"] .navbar .fab,
html[dir="rtl"] .dropdown .fas,
html[dir="rtl"] .dropdown .fab,
html[dir="rtl"] .language-toggle .fas,
html[dir="rtl"] .user-dropdown .fas,
html[lang="ar"] .navbar .fas,
html[lang="ar"] .navbar .fab,
html[lang="ar"] .dropdown .fas,
html[lang="ar"] .dropdown .fab,
html[lang="ar"] .language-toggle .fas,
html[lang="ar"] .user-dropdown .fas {
  transform: none !important;
}

/* عدم عكس أيقونات المحتوى الرئيسي */
html[dir="rtl"] .app-main .fas,
html[dir="rtl"] .app-main .fab,
html[lang="ar"] .app-main .fas,
html[lang="ar"] .app-main .fab {
  transform: none !important;
}

/* عدم عكس أيقونات Footer */
html[dir="rtl"] .footer-section .fas,
html[dir="rtl"] .footer-section .fab,
html[lang="ar"] .footer-section .fas,
html[lang="ar"] .footer-section .fab {
  transform: none !important;
}

/* عدم عكس أيقونات الأزرار والكروت */
html[dir="rtl"] .btn .fas,
html[dir="rtl"] .btn .fab,
html[dir="rtl"] .card .fas,
html[dir="rtl"] .card .fab,
html[dir="rtl"] .carousel .fas,
html[dir="rtl"] .carousel .fab,
html[lang="ar"] .btn .fas,
html[lang="ar"] .btn .fab,
html[lang="ar"] .card .fas,
html[lang="ar"] .card .fab,
html[lang="ar"] .carousel .fas,
html[lang="ar"] .carousel .fab {
  transform: none !important;
}

/* عدم عكس أيقونات Contact Info */
html[dir="rtl"] .contact-item .fas,
html[dir="rtl"] .contact-item .fab,
html[lang="ar"] .contact-item .fas,
html[lang="ar"] .contact-item .fab {
  transform: none !important;
}

/* إصلاح ترتيب الأيقونات في User Dropdown للعربية */
html[dir="rtl"] .user-dropdown .dropdown-item,
html[lang="ar"] .user-dropdown .dropdown-item {
  flex-direction: row-reverse !important;
  display: flex !important;
  align-items: center !important;
}

html[dir="rtl"] .user-dropdown .menu-icon,
html[lang="ar"] .user-dropdown .menu-icon {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
  order: 2 !important;
}

html[dir="rtl"] .user-dropdown .dropdown-item span:not(.menu-icon),
html[lang="ar"] .user-dropdown .dropdown-item span:not(.menu-icon) {
  order: 1 !important;
  text-align: right !important;
  flex: 1 !important;
}

/* إصلاح SVG في User Dropdown */
html[dir="rtl"] .user-dropdown svg,
html[lang="ar"] .user-dropdown svg {
  order: 2 !important;
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* إصلاح app-keenicon في User Dropdown */
html[dir="rtl"] .user-dropdown app-keenicon,
html[lang="ar"] .user-dropdown app-keenicon {
  order: 2 !important;
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* إصلاح FontAwesome icons في User Dropdown */
html[dir="rtl"] .user-dropdown .fas,
html[dir="rtl"] .user-dropdown .fab,
html[dir="rtl"] .user-dropdown .far,
html[lang="ar"] .user-dropdown .fas,
html[lang="ar"] .user-dropdown .fab,
html[lang="ar"] .user-dropdown .far {
  order: 2 !important;
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

/* إصلاح عام للـ dropdown في العربية */
html[dir="rtl"] .dropdown-item,
html[lang="ar"] .dropdown-item {
  text-align: right !important;
  direction: rtl !important;
}

/* استثناءات - عدم عكس أيقونات معينة */
html[dir="rtl"] .app-sidebar .fa-chevron-down,
html[dir="rtl"] .app-sidebar .fa-chevron-up,
html[dir="rtl"] .app-sidebar .fa-chevron-left,
html[dir="rtl"] .app-sidebar .fa-chevron-right,
html[lang="ar"] .app-sidebar .fa-chevron-down,
html[lang="ar"] .app-sidebar .fa-chevron-up,
html[lang="ar"] .app-sidebar .fa-chevron-left,
html[lang="ar"] .app-sidebar .fa-chevron-right {
  transform: scaleX(1) !important;
}

/* إصلاح أيقونات الأسهم */
html[dir="rtl"] .app-sidebar .fa-chevron-left,
html[lang="ar"] .app-sidebar .fa-chevron-left {
  transform: rotate(180deg) !important;
}

html[dir="rtl"] .app-sidebar .fa-chevron-right,
html[lang="ar"] .app-sidebar .fa-chevron-right {
  transform: rotate(180deg) !important;
}

/* إصلاح إضافي للأيقونات */
.rtl .store-icon,
html[lang="ar"] .store-icon,
html[dir="rtl"] .store-icon {
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: auto !important;
  height: auto !important;
}

.rtl .store-icon i,
html[lang="ar"] .store-icon i,
html[dir="rtl"] .store-icon i {
  padding: 0 !important;
  margin: 0 !important;
  font-size: 1rem !important; /* تصغير الأيقونات في العربية */
  width: auto !important;
  height: auto !important;
  transform: scale(0.8) !important; /* تصغير إضافي للعربية */
}
@import "./assets/sass/style.scss";
// Replace above style with this css file to enable RTL styles
// @import "./assets/css/style.rtl";
@import './assets/sass/plugins.scss';
// @import "./assets/css/style.rtl.css";
@import "./assets/sass/style.angular.scss";

// Keenicons - High quality and pixel perfect font icons available in 3 styles, duotone, outline and solid for Metronic elements
@import "./assets/plugins/keenicons/duotone/style.css";
@import "./assets/plugins/keenicons/outline/style.css";
@import "./assets/plugins/keenicons/solid/style.css";

