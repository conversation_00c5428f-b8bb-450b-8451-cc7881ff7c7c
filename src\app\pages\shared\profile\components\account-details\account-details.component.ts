import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-account-details',
  standalone: true,
  imports: [CommonModule, RouterModule, TranslateModule],
  templateUrl: './account-details.component.html',
  styleUrl: './account-details.component.scss',
})
export class AccountDetailsComponent {
  @Input() user: any = {};

  // Sample data for specializations and locations
  specializations: string[] = [' Gold account'];

  constructor(private router: Router) {}

  navigateToRegistrationPapers() {
    this.router.navigate(['/profile/registration-papers']);
  }
}
