.image-input {
  position: relative;
  display: inline-block;
  border-radius: 0.475rem;
  background-repeat: no-repeat;
  background-size: cover;

  .image-input-wrapper {
    width: 120px;
    height: 120px;
    border-radius: 0.475rem;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  &.image-input-empty .image-input-wrapper {
    background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="%23cccccc" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"%3E%3C/path%3E%3C/svg%3E');
    background-color: #f8f9fa;
  }

  [data-kt-image-input-action] {
    cursor: pointer;
    position: absolute;
    transform: translate(-50%, -50%);

    &[data-kt-image-input-action="change"] {
      left: 100%;
      top: 0;
    }

    &[data-kt-image-input-action="remove"] {
      left: 100%;
      top: 100%;
    }

    input {
      width: 0 !important;
      height: 0 !important;
      overflow: hidden;
      opacity: 0;
    }
  }
}

// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .form-label {
    text-align: right;
    justify-content: flex-end;
  }

  .form-control {
    text-align: right;
  }

  .required {
    margin-left: 0;
    margin-right: 2px;
  }

  .card-footer {
    text-align: left;

    .btn {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }

  .invalid-feedback {
    text-align: right;
  }
}
