// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .d-flex {
    &.flex-wrap.flex-sm-nowrap {
      flex-direction: row-reverse;
    }

    &.justify-content-between {
      flex-direction: row;
    }

    &.my-4 {
      flex-direction: row;
      justify-content: center !important;
    }

    &.flex-wrap {
      justify-content: center !important;
    }
  }

  .me-3, .me-6, .me-2 {
    margin-right: 0 !important;
    margin-left: 0.75rem !important;
  }

  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  .border-dashed {
    text-align: center;
  }

  .fw-bold {
    text-align: center;
  }

  // إصلاح أيقونة الكاميرا في العربية
  .btn-icon,
  .btn-circle {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    i {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      margin: 0 !important;
    }
  }

  // إصلاح موضع الكاميرا
  .position-absolute {
    &.bottom-0 {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }

  // محاذاة الأرقام والنصوص في العربية
  .fs-2.fw-bolder {
    text-align: center !important;
    direction: rtl !important;
    font-family: 'Arial', sans-serif !important;
  }

  .border-dashed {
    .d-flex.align-items-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6 {
      text-align: center !important;
    }
  }

  // تحسين عرض الأرقام العربية
  .fs-2 {
    font-weight: bold !important;
    line-height: 1.2 !important;
  }

  // تحسين التخطيط العام
  .flex-grow-1 {
    .d-flex.justify-content-between {
      flex-direction: row !important;

      .d-flex.flex-column {
        text-align: right !important;
        margin-right: 0 !important;
        margin-left: auto !important;

        // محاذاة الاسم والـ badge لليمين في العربية
        .name-section {
          justify-content: flex-end !important;
          text-align: right !important;
          direction: rtl !important;
          width: 100% !important;
          align-items: flex-start !important;

          .user-name {
            text-align: right !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
          }

          .user-badge {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
          }
        }

        .d-flex.align-items-center.mb-2 {
          justify-content: flex-start !important;
          text-align: left !important;
          direction: ltr !important;
          width: 100% !important;

          .text-gray-800.fs-2.fw-bolder {
            text-align: left !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
          }

          .badge {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }

        // محاذاة معلومات الاتصال
        .d-flex.flex-wrap {
          justify-content: flex-start !important;
          text-align: left !important;
          direction: ltr !important;

          a {
            direction: ltr !important;
            text-align: left !important;
          }
        }
      }

      .d-flex.my-4 {
        margin-right: auto !important;
        margin-left: 0 !important;
      }
    }
  }

  // إزالة المساحة الزائدة
  .pe-8 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .me-7 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }
}
