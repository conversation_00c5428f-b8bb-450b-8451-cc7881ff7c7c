// RTL Support
:host-context(.rtl) {
  .card {
    direction: rtl;
    text-align: right;
  }

  .d-flex {
    flex-direction: row-reverse;

    &.my-4 {
      flex-direction: row;
    }
  }

  .me-3, .me-6, .me-2 {
    margin-right: 0 !important;
    margin-left: 0.75rem !important;
  }

  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }

  .border-dashed {
    text-align: center;
  }

  .fw-bold {
    text-align: center;
  }

  // إصلاح أيقونة الكاميرا في العربية
  .btn-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    i {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  // محاذاة الأرقام والنصوص في العربية
  .fs-2.fw-bolder {
    text-align: center !important;
    direction: rtl !important;
    font-family: 'Arial', sans-serif !important;
  }

  .border-dashed {
    .d-flex.align-items-center {
      justify-content: center !important;
    }

    .fw-bold.fs-6 {
      text-align: center !important;
    }
  }

  // تحسين عرض الأرقام العربية
  .fs-2 {
    font-weight: bold !important;
    line-height: 1.2 !important;
  }
}
