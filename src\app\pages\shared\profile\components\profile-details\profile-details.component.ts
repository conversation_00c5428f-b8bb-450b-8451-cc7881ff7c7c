import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, NgForm } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { ProfileService } from '../../services/profile.service';

@Component({
  selector: 'app-profile-details',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './profile-details.component.html',
  styleUrl: './profile-details.component.scss',
})
export class ProfileDetailsComponent implements OnChanges {
  @Input() user: any = {};
  @Input() isLoading: boolean = false;
  @Output() saveChanges = new EventEmitter<any>();
  @Output() loadingError = new EventEmitter<void>();
  @ViewChild('profileForm') profileForm: NgForm;

  formData: any = {};
  localLoading: boolean = false;
  constructor(
    private profileService: ProfileService,
    private translate: TranslateService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['user'] && this.user) {
      this.formData = { ...this.user };
    }
  }

  onSubmit() {
    this.localLoading = true;

    const userData = {
      fullName: this.profileForm.value.fullName,
      phone: this.user.phone,
      email: this.user.email,
    };

    this.profileService.updateProfile(this.user.id, userData).subscribe({
      next: (response) => {
        this.localLoading = false;
        console.log(response.data);
        localStorage.setItem('currentUser', JSON.stringify(response.data));
        Swal.fire(
          this.translate.instant('COMMON.SUCCESS'),
          this.translate.instant('PROFILE.PROFILE_UPDATED_SUCCESS'),
          'success'
        );
      },
      error: (error) => {
        this.localLoading = false;
        console.error('Error updating profile:', error);
        Swal.fire(
          this.translate.instant('COMMON.ERROR'),
          this.translate.instant('PROFILE.PROFILE_UPDATE_FAILED'),
          'error'
        );
      },
    });
  }

  showLoadingForOneSecond() {
    this.localLoading = true;
    setTimeout(() => {
      this.localLoading = false;
    }, 1000);
  }
}
