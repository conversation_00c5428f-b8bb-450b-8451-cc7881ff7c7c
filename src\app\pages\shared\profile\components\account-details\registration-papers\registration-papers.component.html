<div class="card mb-5 mb-xl-10">
  <div class="card-header border-0 bg-light-dark-blue">
    <div class="card-title m-0">
      <h3 class="fw-bolder m-0">{{ 'PROFILE.REGISTRATION_PAPERS.TITLE' | translate }}</h3>
    </div>
    <div class="card-toolbar">
      <button class="btn btn-sm btn-dark-blue" (click)="openModal(uploadModal, 'add')">
        <i class="fa fa-plus me-1"></i> {{ 'PROFILE.REGISTRATION_PAPERS.ADD_DOCUMENT' | translate }}
      </button>
    </div>
  </div>

  <div class="card-body border-top p-9">
    <div class="row g-4">
      <div class="col-md-4 col-sm-6" *ngFor="let document of documents">
        <div class="card shadow-sm h-100">
          <div class="card-body text-center position-relative">
            <span class="badge position-absolute top-0 end-0 m-2 {{
                getStatusBadgeClass(document.status)
              }}">
              {{ getTranslatedStatus(document.status) }}
            </span>

            <!-- Preview -->
            <div class="mb-3">
              <img *ngIf="['JPG', 'PNG', 'GIF'].includes(document.type)" [src]="document.fileUrl" class="rounded"
                style="width: 80px; height: 80px; object-fit: cover"
                onerror="this.src='./assets/media/svg/files/image.svg'; this.onerror=null;" />
              <div *ngIf="!['JPG', 'PNG', 'GIF'].includes(document.type)"
                class="d-flex align-items-center justify-content-center bg-light rounded"
                style="width: 80px; height: 80px; margin: auto">
                <span class="text-{{ getFileTypeColor(document.type) }} fw-bold">
                  {{ document.type }}
                </span>
              </div>
            </div>

            <!-- Edit Button -->
            <button class="btn btn-sm btn-light-primary" (click)="openModal(uploadModal, 'edit', document)"
              [disabled]="document.status === 'pending'">
              <i class="fa fa-pen me-1"></i> {{ 'PROFILE.REGISTRATION_PAPERS.EDIT' | translate }}
            </button>

            <div *ngIf="document.status === 'pending'" class="text-muted small mt-2">
              <i class="fa fa-clock me-1"></i> {{ 'PROFILE.REGISTRATION_PAPERS.PENDING_REVIEW' | translate }}
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="col-12 text-center py-5" *ngIf="documents.length === 0">
        <img src="./assets/media/svg/files/upload.svg" class="w-100px mb-3" />
        <h5 class="text-gray-600">{{ 'PROFILE.REGISTRATION_PAPERS.NO_DOCUMENTS' | translate }}</h5>
        <p class="text-muted">{{ 'PROFILE.REGISTRATION_PAPERS.NO_DOCUMENTS_MESSAGE' | translate }}</p>
      </div>
    </div>

    <div class="text-start mt-6">
      <a routerLink="/profile" class="btn btn-primary">
        <i class="fa fa-arrow-left me-2"></i> {{ 'PROFILE.REGISTRATION_PAPERS.BACK_TO_PROFILE' | translate }}
      </a>
    </div>
  </div>
</div>

<!-- Modal -->
<ng-template #uploadModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">
      {{ formData.mode === "edit" ? ('PROFILE.REGISTRATION_PAPERS.EDIT_DOCUMENT' | translate) :
      ('PROFILE.REGISTRATION_PAPERS.UPLOAD_DOCUMENT' | translate) }}
    </h5>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>

  <div class="modal-body">
    <!-- File Input -->
    <div class="mb-3">
      <label for="fileInput" class="form-label">
        {{
        formData.mode === "edit"
        ? ('PROFILE.REGISTRATION_PAPERS.REPLACE_DOCUMENT' | translate)
        : ('PROFILE.REGISTRATION_PAPERS.SELECT_DOCUMENT' | translate)
        }}
      </label>
      <input type="file" class="form-control" id="fileInput" (change)="onFileSelected($event)"
        accept="image/*,.pdf,.doc,.docx,.xls,.xlsx" />
    </div>

    <!-- Preview -->
    <div *ngIf="formData.preview" class="text-center mb-3">
      <img *ngIf="
          formData.file?.type?.startsWith('image/') ||
          formData.preview?.startsWith('data:image')
        " [src]="formData.preview" style="width: 80px; height: 80px; object-fit: cover" class="rounded" />
      <div *ngIf="
          !(
            formData.file?.type?.startsWith('image/') ||
            formData.preview?.startsWith('data:image')
          )
        " class="text-muted">
        <i class="fa fa-file me-2"></i>
        {{ formData.file?.name || ('PROFILE.REGISTRATION_PAPERS.CURRENT_FILE_REMAINS' | translate) }}
      </div>
    </div>

    <!-- Description Input Removed -->

    <div class="alert alert-info small">
      <i class="fa fa-info-circle me-1"></i>
      {{
      formData.mode === "edit"
      ? ('PROFILE.REGISTRATION_PAPERS.CHANGES_REVIEW_MESSAGE' | translate)
      : ('PROFILE.REGISTRATION_PAPERS.UPLOAD_REVIEW_MESSAGE' | translate)
      }}
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      {{ 'PROFILE.REGISTRATION_PAPERS.CANCEL' | translate }}
    </button>
    <button type="button" class="btn btn-primary" [disabled]="(formData.mode === 'add' && !formData.file) || isLoading"
      (click)="saveDocument()">
      <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-1" role="status"></span>
      {{ formData.mode === "edit" ? ('PROFILE.REGISTRATION_PAPERS.UPDATE_DOCUMENT' | translate) :
      ('PROFILE.REGISTRATION_PAPERS.UPLOAD' | translate) }}
    </button>
  </div>
</ng-template>